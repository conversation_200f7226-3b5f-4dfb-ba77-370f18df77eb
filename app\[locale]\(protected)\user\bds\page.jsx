import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "@/i18n/navigation";
import PropertyList from "./PropertyList";
import { getPropertyByUser } from "@/app/actions/server/property";
import { AlertCircleIcon } from "lucide-react";
import { getTranslations } from "next-intl/server";
import { Alert, AlertDescription } from "@/components/ui/alert";

export default async function PropertyListPage() {
  const t = await getTranslations("UserPropertiesPage");

  // Fetch both initial data and filter counts in parallel for better performance
  const [propertiesResult, filterCountsResult] = await Promise.all([
    getPropertyByUser(null, 1, 10), // Get all properties by default (no status filter)
    getPropertyByUser("counts"), // Get filter counts
  ]);

  // Extract items array from the paginated response
  const propertyData =
    propertiesResult && propertiesResult?.isSuccess && propertiesResult?.data && propertiesResult?.data?.items ? propertiesResult?.data?.items : [];

  // Extract filter counts data
  const filterCountsData = filterCountsResult && filterCountsResult?.isSuccess && filterCountsResult?.data ? filterCountsResult?.data : null;

  return (
    <div className="min-h-screen bg-white p-6">
      <div className="mb-8">
        <div className="mb-2 flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{t("title")}</h1>
            <p className="mt-1 text-sm text-gray-500">{t("description")}</p>
          </div>
          <Button asChild className="gap-2 bg-teal-500 hover:bg-teal-600">
            <Link href="/user/bds/new">{t("createButton")}</Link>
          </Button>
        </div>
        <Alert className="mb-2 bg-teal-50 border-teal-400">
          <AlertDescription className="mb-0 flex gap-2 items-center">
            <AlertCircleIcon className="h-4 w-4" />
            {t("aleatAnalysis")}
          </AlertDescription>
        </Alert>
      </div>
      {/* Pass both initial data and filter counts from server to avoid duplicate API calls */}
      <PropertyList initialData={propertyData} initialFilterCounts={filterCountsData} />
    </div>
  );
}
