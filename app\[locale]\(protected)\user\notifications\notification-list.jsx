"use client";
import { useEffect, useState, useMemo } from "react";
import { getNotifications, markAsRead } from "@/app/actions/server/notification";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import { Bell, Check, CheckCheck, ExternalLink, ChevronLeft, ChevronRight } from "lucide-react";
import { format } from "date-fns";
import { vi, enUS } from "date-fns/locale";
import { toast } from "@/hooks/use-toast";
import { useTranslations, useLocale } from "next-intl";
import { Link } from "@/i18n/navigation";
import { useRouter, useSearchParams } from "next/navigation";
import { getNotificationTypes, getNotificationActionText, isActionableNotification, getNotificationCategory } from "@/lib/constants/notificationTypes";
import { formatNotificationTime } from "@/lib/utils";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

export default function NotificationList() {
  const t = useTranslations("UserNotificationsPage");
  const locale = useLocale();
  const dateLocale = locale === "vi" ? vi : enUS;
  const router = useRouter();
  const searchParams = useSearchParams();

  const NOTIFICATION_TYPES_TAB = useMemo(() => {
    const types = getNotificationTypes(t);
    // Add the actual React elements for icons
    return types.map((type) => ({
      ...type,
      icon: <type.icon className="h-4 w-4" />,
    }));
  }, [t]);

  const [activeTab, setActiveTab] = useState(null);
  const [loading, setLoading] = useState(false);
  const [notifications, setNotifications] = useState({});
  const [pagination, setPagination] = useState({});
  const [loadedTabs, setLoadedTabs] = useState(new Set());

  // Initialize state only once
  useEffect(() => {
    const initialNotifications = {};
    const initialPagination = {};
    NOTIFICATION_TYPES_TAB.forEach((type) => {
      initialNotifications[type.id] = [];
      initialPagination[type.id] = { page: 1, totalPages: 1, totalItems: 0 };
    });
    setNotifications(initialNotifications);
    setPagination(initialPagination);
  }, []);

  // Handle URL parameter changes and initial load
  useEffect(() => {
    // Don't proceed if component is not fully initialized
    if (Object.keys(notifications).length === 0) return;

    const tabType = searchParams.get("type");
    const validTabType = tabType && NOTIFICATION_TYPES_TAB.some((tab) => tab.id.toLowerCase() === tabType.toLowerCase());

    const initialTab = validTabType
      ? NOTIFICATION_TYPES_TAB.find((tab) => tab.id.toLowerCase() === tabType.toLowerCase()).id
      : NOTIFICATION_TYPES_TAB[0].id;

    setActiveTab(initialTab);

    // Only load if we haven't loaded this tab before
    if (!loadedTabs.has(initialTab)) {
      loadNotifications(initialTab, 1);
      setLoadedTabs((prev) => new Set([...prev, initialTab]));
    }
  }, [searchParams, NOTIFICATION_TYPES_TAB, notifications]);

  const loadNotifications = async (category = activeTab, page = 1) => {
    if (!category) return;

    setLoading(true);
    try {
      const response = await getNotifications({
        category, // Changed from 'type' to 'category'
        page,
        limit: 10,
      });
      if (response?.isSuccess) {
        // Group notifications by category based on their type
        const items = response.data.items || [];
        const filteredItems = items.filter(item => {
          const itemCategory = getNotificationCategory(item.type);
          return itemCategory === category;
        });

        setNotifications((prev) => ({
          ...prev,
          [category]: filteredItems,
        }));
        setPagination((prev) => ({
          ...prev,
          [category]: {
            page: response.data.currentPage || 1,
            totalPages: response.data.pageCount || 1,
            totalItems: response.data.totalCount || 0,
          },
        }));
      } else {
        toast({
          variant: "destructive",
          title: t("loadingErrorTitle"),
          description: response.message || t("loadingErrorMessage"),
        });
      }
    } catch (error) {
      console.error("Error loading notifications:", error);
      toast({
        variant: "destructive",
        title: t("loadingErrorTitle"),
        description: t("loadingGenericError"),
      });
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (value) => {
    if (value === activeTab) return;

    setActiveTab(value);

    // Update URL with the selected tab type
    const params = new URLSearchParams(searchParams.toString());
    params.set("type", value);
    router.push(`?${params.toString()}`, { scroll: false });

    // Load notifications for the selected tab if not already loaded
    if (!loadedTabs.has(value)) {
      loadNotifications(value, 1);
      setLoadedTabs((prev) => new Set([...prev, value]));
    }
  };

  const handlePageChange = (type, newPage) => {
    if (newPage >= 1 && newPage <= pagination[type]?.totalPages) {
      loadNotifications(type, newPage);
    }
  };

  const handleMarkAsRead = async (id) => {
    try {
      const response = await markAsRead({ ids: [id] });
      if (response?.isSuccess) {
        setNotifications((prev) => {
          const updatedNotifications = { ...prev };
          Object.keys(updatedNotifications).forEach((type) => {
            updatedNotifications[type] = updatedNotifications[type].map((notif) => (notif.id === id ? { ...notif, isRead: true } : notif));
          });
          return updatedNotifications;
        });
      } else {
        toast({
          variant: "destructive",
          title: t("markReadErrorTitle"),
          description: response.message || t("markReadErrorMessage"),
        });
      }
    } catch (error) {
      console.error("Error marking notification as read:", error);
      toast({
        variant: "destructive",
        title: t("markReadErrorTitle"),
        description: t("markReadGenericError"),
      });
    }
  };

  const handleMarkAllAsRead = async () => {
    const unreadIds = notifications[activeTab]?.filter((n) => !n.isRead)?.map((n) => n.id) || [];

    if (unreadIds.length === 0) return;

    try {
      const response = await markAsRead({ ids: unreadIds });

      if (response?.isSuccess) {
        setNotifications((prev) => {
          const updatedNotifications = { ...prev };
          updatedNotifications[activeTab] = updatedNotifications[activeTab].map((notif) => ({
            ...notif,
            isRead: true,
          }));
          return updatedNotifications;
        });
        toast({
          title: t("markAllReadSuccessTitle"),
          description: t("markAllReadSuccessMessage"),
        });
      } else {
        toast({
          variant: "destructive",
          title: t("markAllReadErrorTitle"),
          description: response.message || t("markAllReadErrorMessage"),
        });
      }
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
      toast({
        variant: "destructive",
        title: t("markAllReadErrorTitle"),
        description: t("markAllReadGenericError"),
      });
    }
  };

  const getNotificationIcon = (notificationType) => {
    // Get the category for this notification type
    const category = getNotificationCategory(notificationType);
    // Find the matching category tab
    const categoryTab = NOTIFICATION_TYPES_TAB.find((tab) => tab.id === category);
    return categoryTab ? categoryTab.icon : <Bell className="h-4 w-4" />;
  };

  // Handle notification click for actionable notifications
  const handleNotificationClick = async (notification) => {
    // Mark as read if not already read
    if (!notification.isRead) {
      await handleMarkAsRead(notification.id);
    }

    // Navigate to action URL if available
    if (notification.actionUrl) {
      try {
        // Check if it's an external URL
        if (notification.actionUrl.startsWith("http://") || notification.actionUrl.startsWith("https://")) {
          window.open(notification.actionUrl, "_blank");
        } else {
          const absoluteUrlObject = new URL(notification.actionUrl, window.location.href);
          const absoluteUrl = absoluteUrlObject.href; 

          window.open(absoluteUrl, "_blank");
        }
      } catch (error) {
        console.error("Error navigating to notification action:", error);
        toast({
          variant: "destructive",
          title: t("navigationErrorTitle"),
          description: t("navigationErrorMessage"),
        });
      }
    }
  };

  // If activeTab is not set yet, show loading state
  if (!activeTab) {
    return <LoadingSpinner text={t("loadingNotifications")} />;
  }

  return (
    <Tabs value={activeTab} onValueChange={handleTabChange}>
      <TabsList className="grid grid-cols-2 sm:grid-cols-5 mb-8">
        {NOTIFICATION_TYPES_TAB.map((type) => (
          <TabsTrigger key={type.id} value={type.id} className="flex items-center gap-2">
            {type.icon}
            <span className="hidden sm:inline">{type.label}</span>
          </TabsTrigger>
        ))}
      </TabsList>

      {NOTIFICATION_TYPES_TAB.map((type) => (
        <TabsContent key={type.id} value={type.id} className="space-y-4">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">{type.label}</h2>
            {notifications[type.id]?.some((n) => !n.isRead) && (
              <Button variant="outline" size="sm" onClick={handleMarkAllAsRead} className="flex items-center gap-2">
                <CheckCheck className="h-4 w-4" />
                <span>{t("markAllReadButton")}</span>
              </Button>
            )}
          </div>

          {loading && activeTab === type.id ? (
            <LoadingSpinner text={t("loadingNotifications")} />
          ) : notifications[type.id]?.length > 0 ? (
            <>
              <div className="space-y-3">
                {notifications[type.id].map((notification) => {
                  const isActionable = isActionableNotification(notification);
                  return (
                    <Card
                      key={notification.id}
                      className={`transition-all duration-200 ${notification.isRead ? "bg-white hover:bg-gray-50" : "bg-teal-50 hover:bg-teal-100"} ${
                        isActionable ? "cursor-pointer hover:shadow-md border-l-4 border-l-teal-600" : ""
                      }`}
                      onClick={isActionable ? () => handleNotificationClick(notification) : undefined}
                    >
                      <CardContent className="p-4 flex gap-4 items-start">
                        <div className={`mt-1 p-2 rounded-full ${notification.isRead ? "bg-gray-100 text-gray-500" : "bg-teal-100 text-teal-600"}`}>
                          {getNotificationIcon(notification.type)}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-start justify-between gap-2"></div>
                          <p className={`text-sm mb-2 ${notification.isRead ? "text-gray-600" : "text-gray-700"}`}>
                            {notification.message || notification.content}
                          </p>
                          <div className="flex items-center justify-between">
                            <p className="text-xs text-gray-400">{formatNotificationTime(notification.createdAt, t, locale)}</p>
                            {isActionable && (
                              <div className="flex items-center gap-1 text-xs text-teal-600">
                                <ExternalLink className="h-3 w-3" />
                                <span>{getNotificationActionText(notification, t)}</span>
                              </div>
                            )}
                          </div>

                          {/* Legacy link support */}
                          {notification.link && !isActionable && (
                            <Link href={notification.link} className="text-xs text-teal-600 hover:underline mt-1 inline-block">
                              {t("actionTextDefault")}
                            </Link>
                          )}

                          {/* Debug info for development (remove in production) */}
                          {process.env.NODE_ENV === "development" && isActionable && (
                            <div className="mt-2 p-2 bg-gray-100 rounded text-xs text-gray-500">
                              <div>ActionURL: {notification.actionUrl}</div>
                              <div>PropertyID: {notification.relatedPropertyId}</div>
                              <div>EntityID: {notification.relatedEntityId}</div>
                            </div>
                          )}
                        </div>
                        {!notification.isRead && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation(); // Prevent card click
                              handleMarkAsRead(notification.id);
                            }}
                            className="p-1 rounded-full hover:bg-gray-200 text-gray-400 hover:text-gray-600 mt-1"
                            title={t("markReadSuccessMessage")}
                          >
                            <Check className="h-4 w-4" />
                          </button>
                        )}
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
              {pagination[type.id]?.totalPages > 1 && (
                <div className="flex justify-center items-center space-x-2 mt-6">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => handlePageChange(type.id, pagination[type.id].page - 1)}
                    disabled={pagination[type.id].page <= 1 || loading}
                    aria-label={t("previousPageButton")}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <span className="text-sm text-gray-600">
                    Trang {pagination[type.id].page} / {pagination[type.id].totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => handlePageChange(type.id, pagination[type.id].page + 1)}
                    disabled={pagination[type.id].page >= pagination[type.id].totalPages || loading}
                    aria-label={t("nextPageButton")}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-10 text-gray-500">
              <Bell className="mx-auto h-12 w-12 text-gray-400 mb-3" />
              {t("noNotificationsMessage")}
            </div>
          )}
        </TabsContent>
      ))}
    </Tabs>
  );
}
