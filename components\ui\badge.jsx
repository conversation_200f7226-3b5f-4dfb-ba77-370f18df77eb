import * as React from "react"
import { cva } from "class-variance-authority";

import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 h-5 ml-0 mt-0",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground shadow-sm",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground shadow-sm",
        outline: "text-foreground",
        ghost: "",
        primary: "bg-primary text-primary-foreground",
      },
      rounded: {
        default: "rounded-md",
        full: "rounded-full",
      },
      height: {
        default: "h-5",
        sm: "h-4",
        fit: "h-fit",
      },
    },
    defaultVariants: {
      variant: "default",
      rounded: "default",
      height: "default",
    },
  }
)

function Badge({
  className,
  variant,
  rounded,
  height,
  ...props
}) {
  return (<div className={cn(badgeVariants({ variant, rounded, height }), className)} {...props} />);
}

export { Badge, badgeVariants }
