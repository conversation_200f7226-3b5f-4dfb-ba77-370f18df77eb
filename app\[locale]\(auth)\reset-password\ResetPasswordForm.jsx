"use client";
import React from "react";
import ButtonLoading from "@/components/ui/ButtonLoading";
import { useActionState } from "react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { resetPassword } from "@/app/actions/server/authenticate";
import { Label } from "@/components/ui/label";
import { CircleAlert, Lock } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Link } from "@/i18n/navigation";
import { useTranslations } from "next-intl";

const initialState = {
  errors: {},
  message: null,
  fieldValues: {
    userId: "",
    token: "",
    newPassword: "",
    confirmPassword: "",
  },
};

export default function ResetPasswordForm({ userId, token }) {
  const t = useTranslations("ResetPasswordPage");
  const [state, formAction, isPending] = useActionState(resetPassword, initialState);

  let isError = state.message && (state.message.includes("Token not found") || state.message.includes("Token expired") || state.message.includes("Token has been used"));

  return (
    <form className="mt-8 space-y-6" action={formAction}>
      {/* Hidden fields for userId and token */}
      <input type="hidden" name="userId" value={userId} />
      <input type="hidden" name="token" value={token} />

      {state.message && (
        <Alert variant="destructive">
          <CircleAlert className="h-4 w-4" />
          <AlertTitle className="text-red-500">{t("errorTitle")}</AlertTitle>
          <AlertDescription>
            {state.message}
            {isError ? (
              <p>
                Vui lòng{" "}
                <Link href="/quen-mat-khau" className="text-blue-500 hover:underline">
                  yêu cầu một liên kết đặt lại mật khẩu mới
                </Link>
                .
              </p>
            ) : null}
          </AlertDescription>
        </Alert>
      )}

      <div className="space-y-2">
        <Label htmlFor="newPassword">{t("newPasswordLabel")}</Label>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Lock className="h-5 w-5 text-gray-400" />
          </div>
          <Input
            id="newPassword"
            type="password"
            name="newPassword"
            placeholder={t("newPasswordPlaceholder")}
            defaultValue={state.fieldValues?.newPassword}
            className="pl-10"
            required
            autoComplete="new-password"
          />
          {state.errors?.newPassword && <p className="mt-1 text-xs text-red-500">{state.errors.newPassword[0]}</p>}
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="confirmPassword">{t("confirmPasswordLabel")}</Label>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Lock className="h-5 w-5 text-gray-400" />
          </div>
          <Input
            id="confirmPassword"
            type="password"
            name="confirmPassword"
            placeholder={t("confirmPasswordPlaceholder")}
            defaultValue={state.fieldValues?.confirmPassword}
            className="pl-10"
            required
            autoComplete="new-password"
          />
          {state.errors?.confirmPassword && <p className="mt-1 text-xs text-red-500">{state.errors.confirmPassword[0]}</p>}
        </div>
      </div>

      <ButtonLoading type="submit" showLoading={isPending} title={t("submitButton")} />

      <div className="text-center">
        <Link href="/dang-nhap" className="text-sm font-medium text-primary hover:underline">
          {t("backToLoginLink")}
        </Link>
      </div>
    </form>
  );
}
