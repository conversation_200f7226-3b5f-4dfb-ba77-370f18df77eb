"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { createContactRequest } from "@/app/actions/server/contactRequest";
import { Mail, Phone, User, MessageSquare, Check } from "lucide-react";
import { useTranslations } from "next-intl";

export default function PropertyContactForm({ isOpen, onClose, propertyId, ownerId }) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    note: ""
  });
  const [errors, setErrors] = useState({});
  const { toast } = useToast();
  const t = useTranslations("PropertyContactForm");

  // Vietnamese phone number validation
  const validatePhoneNumber = (phone) => {
    // Vietnamese phone number patterns:
    // - Mobile: 03x, 05x, 07x, 08x, 09x (10 digits total)
    // - Landline: 02x (10-11 digits total)
    const phoneRegex = /^(\+84|84|0)?((3[2-9])|(5[6|8|9])|(7[0|6-9])|(8[1-6|8|9])|(9[0-4|6-9]))[0-9]{7}$|^(\+84|84|0)?(2[0-9])[0-9]{8,9}$/;
    return phoneRegex.test(phone.replace(/\s+/g, ''));
  };

  // Email validation
  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // Form validation
  const validateForm = () => {
    const newErrors = {};

    // Name validation
    if (!formData.name.trim()) {
      newErrors.name = t("nameRequired");
    }

    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = t("emailRequired");
    } else if (!validateEmail(formData.email)) {
      newErrors.email = t("emailInvalid");
    }

    // Phone validation
    if (!formData.phone.trim()) {
      newErrors.phone = t("phoneRequired");
    } else if (!validatePhoneNumber(formData.phone)) {
      newErrors.phone = t("phoneInvalid");
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined
      }));
    }
  };

  const handleContactSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      const form = new FormData();
      form.append("name", formData.name);
      form.append("email", formData.email);
      form.append("phone", formData.phone);
      form.append("note", formData.note);
      form.append("propertyId", propertyId);
      form.append("propertyOwnerId", ownerId);
      
      const response = await createContactRequest(null, form);
      
      if (response?.isSuccess) {
        setShowSuccessMessage(true);
      } else {
        toast({
          title: t("errorTitle"),
          description: response.message || t("submitError"),
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error submitting contact request:", error);
      toast({
        title: t("errorTitle"),
        description: t("submitGenericError"),
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const resetContactForm = () => {
    setFormData({
      name: "",
      email: "",
      phone: "",
      note: ""
    });
    setErrors({});
    setShowSuccessMessage(false);
  };
  
  const handleContactModalClose = () => {
    onClose();
    // Only reset the form after a delay to avoid visual glitches
    setTimeout(resetContactForm, 300);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleContactModalClose}>
      <DialogContent className="max-w-md">
        {!showSuccessMessage ? (
          <>
            <DialogHeader>
              <DialogTitle>{t("title")}</DialogTitle>
              <DialogDescription>
                {t("description")}
              </DialogDescription>
            </DialogHeader>
            
            <form onSubmit={handleContactSubmit} className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="name">{t("nameLabel")} <span className="text-red-500">*</span></Label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <User className="h-4 w-4 text-gray-400" />
                  </div>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder={t("namePlaceholder")}
                    className={`pl-10 ${errors.name ? 'border-red-500' : ''}`}
                    required
                  />
                </div>
                {errors.name && (
                  <p className="text-sm text-red-500 mt-1">{errors.name}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="email">{t("emailLabel")} <span className="text-red-500">*</span></Label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Mail className="h-4 w-4 text-gray-400" />
                  </div>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder={t("emailPlaceholder")}
                    className={`pl-10 ${errors.email ? 'border-red-500' : ''}`}
                    required
                  />
                </div>
                {errors.email && (
                  <p className="text-sm text-red-500 mt-1">{errors.email}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="phone">{t("phoneLabel")} <span className="text-red-500">*</span></Label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Phone className="h-4 w-4 text-gray-400" />
                  </div>
                  <Input
                    id="phone"
                    name="phone"
                    type="tel"
                    value={formData.phone}
                    onChange={handleInputChange}
                    placeholder={t("phonePlaceholder")}
                    className={`pl-10 ${errors.phone ? 'border-red-500' : ''}`}
                    required
                  />
                </div>
                {errors.phone && (
                  <p className="text-sm text-red-500 mt-1">{errors.phone}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="note">{t("noteLabel")}</Label>
                <div className="relative">
                  <div className="absolute top-3 left-3 pointer-events-none">
                    <MessageSquare className="h-4 w-4 text-gray-400" />
                  </div>
                  <Textarea
                    id="note"
                    name="note"
                    value={formData.note}
                    onChange={handleInputChange}
                    placeholder={t("notePlaceholder")}
                    className="pl-10 min-h-[100px]"
                  />
                </div>
              </div>
              
              <DialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
                <Button type="button" variant="outline" onClick={handleContactModalClose} disabled={isSubmitting}>
                  {t("cancelButton")}
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <span className="animate-spin mr-2">⏳</span> {t("submittingButton")}
                    </>
                  ) : (
                    t("submitButton")
                  )}
                </Button>
              </DialogFooter>
            </form>
          </>
        ) : (
          <>
            <DialogHeader>
              <DialogTitle>{t("successTitle")}</DialogTitle>
            </DialogHeader>
            
            <div className="py-6 flex flex-col items-center justify-center text-center">
              <div className="bg-green-100 p-3 rounded-full mb-4">
                <Check className="h-8 w-8 text-green-600" />
              </div>
              <p className="text-lg font-medium mb-2">{t("successMessage")}</p>
              <p className="text-gray-500 mb-4">{t("successDescription")}</p>
              
              <Button onClick={handleContactModalClose}>
                {t("closeButton")}
              </Button>
            </div>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
