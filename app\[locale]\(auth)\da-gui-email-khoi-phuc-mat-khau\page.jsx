import {Link} from '@/i18n/navigation';;
import { ArrowLeft } from "lucide-react";
import { getTranslations } from 'next-intl/server';

export default async function Page() {
  const t = await getTranslations('PasswordEmailSentPage');

  return (
    <div className="min-h-screen flex pt-8 justify-center bg-background py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8 text-center">
        <h1 className="mb-4 text-center text-2xl font-bold text-gray-900">
          {t('title')}
        </h1>
        <p className="mb-6 text-center text-gray-600">
          {t('message')}
        </p>
        <div className="text-center">
          <Link
            href="/dang-nhap"
            className="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-800"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('backToLoginLink')}
          </Link>
        </div>
      </div>
    </div>
  );
}
