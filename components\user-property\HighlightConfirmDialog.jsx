"use client";
import { useState } from "react";
import { useTranslations } from "next-intl";
import { useAuth } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { AlertTriangle } from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import { MemberRank, PropertyStatus } from "@/lib/enum";
import { getMemberRankTranslationKey } from "@/lib/memberRankUtils";
import { Alert, AlertDescription } from "../ui/alert";

const HighlightConfirmDialog = ({ open, onClose, onConfirm, propertyStatus }) => {
  const t = useTranslations("PropertyCard");
  const tCommon = useTranslations("Common");
  const { profile } = useAuth();
  const walletInfo = profile?.user?.wallet;
  const router = useRouter();
  const [isProcessing, setIsProcessing] = useState(false);

  // Get highlight fee based on member rank
  const getHighlightFee = () => {
    return {
      fee: profile?.highlightFee?.fee || 0,
      rankName: tCommon(getMemberRankTranslationKey(profile?.user?.memberRank || MemberRank.DEFAULT)),
    };
  };

  const highlightFee = getHighlightFee();
  const hasEnoughBalance = walletInfo && walletInfo.balance >= highlightFee?.fee;

  const handleConfirm = async () => {
    if (!hasEnoughBalance) {
      return;
    }

    setIsProcessing(true);
    try {
      await onConfirm();
    } finally {
      setIsProcessing(false);
      onClose();
    }
  };

  const handleTopUp = () => {
    onClose();
    router.push("/user/wallet");
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{t("highlightConfirmTitle")}</DialogTitle>
          <DialogDescription asChild className="text-sm text-gray-600">
            <div>
              {propertyStatus === PropertyStatus.PENDING_APPROVAL && (
                <Alert className="mb-4">
                  <AlertDescription>{t("reviewDescription")}</AlertDescription>
                </Alert>
              )}
              <div className="mb-4 text-sm text-gray-600">
                {t("highlightConfirmMessage", {
                  amount: formatCurrency(highlightFee?.fee),
                  memberRank: highlightFee.rankName,
                })}
              </div>
            </div>
          </DialogDescription>
        </DialogHeader>

        <div className="pb-4">
          {!hasEnoughBalance && (
            <div className="flex items-start gap-2 p-3 bg-amber-50 border border-amber-200 rounded-md mb-4">
              <AlertTriangle className="h-5 w-5 text-amber-500 mt-0.5 shrink-0" />
              <div>
                <p className="text-sm font-medium text-amber-800">{t("insufficientBalanceTitle")}</p>
                <p className="text-sm text-amber-700 mt-1">{t("insufficientBalanceMessage")}</p>
              </div>
            </div>
          )}

          <div className="flex flex-col gap-2 bg-gray-50 p-3 rounded-md">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">{t("currentBalance")}:</span>
              <span className="text-sm font-medium">{formatCurrency(walletInfo?.balance || 0)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">{t("highlightFee")}:</span>
              <span className="text-sm font-medium">{formatCurrency(highlightFee?.fee)}</span>
            </div>
            <div className="border-t border-gray-200 my-1"></div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">{t("balanceAfter")}:</span>
              <span className={`text-sm font-medium ${!hasEnoughBalance ? "text-red-600" : ""}`}>
                {formatCurrency((walletInfo?.balance || 0) - highlightFee?.fee)}
              </span>
            </div>
          </div>
        </div>

        <DialogFooter className="flex flex-col sm:flex-row gap-2">
          <Button variant="ghost" onClick={onClose} className="w-full sm:w-auto">
            {t("cancel")}
          </Button>
          {!hasEnoughBalance ? (
            <Button onClick={handleTopUp} className="w-full sm:w-auto">
              {t("topUpWallet")}
            </Button>
          ) : (
            <Button onClick={handleConfirm} disabled={isProcessing} className="w-full sm:w-auto">
              {isProcessing ? tCommon("processing") : t("confirm")}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default HighlightConfirmDialog;
