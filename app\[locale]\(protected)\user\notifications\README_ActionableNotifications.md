# Actionable Notifications Frontend Implementation

## Overview
The notification-list.jsx component has been updated to support the new actionable notification features, allowing users to click on notifications to navigate directly to relevant pages.

## New Features

### 🎯 **Clickable Notifications**
- Notifications with `actionUrl`, `relatedPropertyId`, or `relatedEntityId` are now clickable
- Visual indicators show which notifications are actionable
- Automatic navigation when notifications are clicked

### 🎨 **Visual Enhancements**
- **Blue left border**: Indicates actionable notifications
- **Mouse pointer icon**: Shows in notification title for actionable items
- **Action text**: Displays appropriate action text based on notification type
- **Hover effects**: Enhanced hover states for better UX

### 🔧 **Smart Navigation**
- **Internal URLs**: Uses Next.js router for seamless navigation
- **External URLs**: Opens in new tab for external links
- **Auto mark as read**: Automatically marks notifications as read when clicked
- **Error handling**: Graceful error handling for navigation failures

## Notification Types & Actions

| Type | Action Text | Navigation |
|------|-------------|------------|
| Contact | "Xem bất động sản" | `/bds/{propertyId}` |
| Transaction | "Xem giao dịch" | `/dashboard/transactions/{transactionId}` |
| WalletUpdate | "Xem ví" | `/dashboard/wallet` |
| System | "Xem thông báo" | `/dashboard/notifications` |
| Promotion | "Xem khuyến mãi" | `/promotions` |
| News | "Đọc tin tức" | `/news` |

## Code Structure

### **New Functions**
1. `handleNotificationClick(notification)` - Handles click events and navigation
2. `isActionableNotification(notification)` - Checks if notification is clickable
3. `getActionText(notification)` - Returns appropriate action text

### **Enhanced UI Components**
- **Card styling**: Added conditional classes for actionable notifications
- **Click handlers**: Proper event handling with stopPropagation
- **Debug info**: Development-only debug information display

## Usage Examples

### **Contact Notification**
```javascript
// Backend creates notification with:
{
  type: "Contact",
  title: "Yêu cầu liên hệ mới",
  message: "Bạn có một yêu cầu liên hệ mới từ John (SĐT: 0123456789)",
  actionUrl: "/bds/property-id-123",
  relatedPropertyId: "property-id-123",
  relatedEntityId: "contact-request-id-456"
}

// Frontend displays:
// - Clickable card with blue left border
// - Mouse pointer icon in title
// - "Xem bất động sản" action text
// - Navigates to property detail page when clicked
```

### **Transaction Notification**
```javascript
// Backend creates notification with:
{
  type: "Transaction",
  title: "Giao dịch thành công",
  message: "Giao dịch nạp tiền 500,000 VND đã được xử lý",
  actionUrl: "/dashboard/transactions/transaction-id-789",
  relatedEntityId: "transaction-id-789"
}

// Frontend displays:
// - "Xem giao dịch" action text
// - Navigates to transaction details when clicked
```

## Development Features

### **Debug Information**
In development mode, actionable notifications show debug info:
- ActionURL value
- PropertyID value  
- EntityID value

### **Error Handling**
- Navigation errors are caught and displayed as toast messages
- Fallback behavior for malformed URLs
- Graceful degradation for missing data

## Backward Compatibility

- **Legacy links**: Still supports old `notification.link` field
- **Non-actionable**: Regular notifications work as before
- **Gradual migration**: New and old notification formats coexist

## Testing Scenarios

1. **Contact Notification Click**: Should navigate to property detail page
2. **Mark as Read**: Should mark notification as read when clicked
3. **External Links**: Should open in new tab
4. **Error Handling**: Should show error toast for invalid URLs
5. **Legacy Support**: Should still work with old notification format

## Future Enhancements

- **Deep linking**: Support for specific sections within pages
- **Analytics**: Track notification click rates
- **Rich actions**: Multiple action buttons per notification
- **Push notifications**: Integration with browser push notifications
