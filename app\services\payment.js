/**
 * Payment gateway integration service
 */

// Configuration would normally come from environment variables
const PAYMENT_CONFIG = {
  momo: {
    partnerId: process.env.MOMO_PARTNER_ID || "MOMO_PARTNER_ID",
    partnerCode: process.env.MOMO_PARTNER_CODE || "MOMO_PARTNER_CODE",
    accessKey: process.env.MOMO_ACCESS_KEY || "MOMO_ACCESS_KEY",
    secretKey: process.env.MOMO_SECRET_KEY || "MOMO_SECRET_KEY",
    endpoint: process.env.MOMO_ENDPOINT || "https://test-payment.momo.vn/v2/gateway/api/create",
  }
  // Add configuration for other payment methods like card processing
};

/**
 * Create a payment with MoMo
 */
export async function createMomoPayment(amount, orderId, returnUrl, notifyUrl, extraData = {}) {
  try {
    const requestId = `${Date.now()}_${Math.floor(Math.random() * 1000)}`;
    const orderInfo = `Nạp tiền vào ví #${orderId}`;
    
    // Create signature based on MoMo's requirements
    const rawSignature = `partnerCode=${PAYMENT_CONFIG.momo.partnerCode}&accessKey=${PAYMENT_CONFIG.momo.accessKey}&requestId=${requestId}&amount=${amount}&orderId=${orderId}&orderInfo=${orderInfo}&returnUrl=${returnUrl}&notifyUrl=${notifyUrl}&extraData=${JSON.stringify(extraData)}`;
    
    // In production, use crypto library to create HMAC SHA256 signature
    const signature = await createHmacSignature(rawSignature, PAYMENT_CONFIG.momo.secretKey);
    
    const response = await fetch(PAYMENT_CONFIG.momo.endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        partnerCode: PAYMENT_CONFIG.momo.partnerCode,
        accessKey: PAYMENT_CONFIG.momo.accessKey,
        requestId: requestId,
        amount: amount,
        orderId: orderId,
        orderInfo: orderInfo,
        returnUrl: returnUrl,
        notifyUrl: notifyUrl,
        extraData: JSON.stringify(extraData),
        requestType: "captureWallet",
        signature: signature,
      }),
    });
    
    const result = await response.json();
    
    if (result.resultCode === 0) {
      // Success - return payment URL
      return {
        success: true,
        paymentUrl: result.payUrl,
        orderId: orderId,
        transactionId: result.transactionId,
      };
    } else {
      return {
        success: false,
        error: result.message || "Không thể tạo giao dịch MoMo",
        code: result.resultCode,
      };
    }
  } catch (error) {
    console.error("MoMo payment error:", error);
    return {
      success: false,
      error: "Đã xảy ra lỗi khi kết nối với MoMo",
    };
  }
}

/**
 * Verify MoMo payment status
 */
export async function verifyMomoPayment(orderId) {
  try {
    // In a real implementation, you would query MoMo's API to check payment status
    const requestId = `${Date.now()}_verify`;
    
    // Create signature
    const rawSignature = `partnerCode=${PAYMENT_CONFIG.momo.partnerCode}&accessKey=${PAYMENT_CONFIG.momo.accessKey}&requestId=${requestId}&orderId=${orderId}`;
    const signature = await createHmacSignature(rawSignature, PAYMENT_CONFIG.momo.secretKey);
    
    const response = await fetch(`${PAYMENT_CONFIG.momo.endpoint}/query`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        partnerCode: PAYMENT_CONFIG.momo.partnerCode,
        accessKey: PAYMENT_CONFIG.momo.accessKey,
        requestId: requestId,
        orderId: orderId,
        signature: signature,
      }),
    });
    
    const result = await response.json();
    
    return {
      success: result.resultCode === 0,
      verified: result.resultCode === 0 && result.transactionStatus === 0,
      amount: result.amount,
      transactionId: result.transId,
      message: result.message,
    };
  } catch (error) {
    console.error("MoMo verification error:", error);
    return { success: false, error: "Verification failed" };
  }
}

/**
 * Helper function to create HMAC SHA256 signatures
 */
async function createHmacSignature(message, secretKey) {
  // In browser environment, use SubtleCrypto API
  // In Node.js environment, use the crypto module
  // This is a simplified example
  const encoder = new TextEncoder();
  const keyData = encoder.encode(secretKey);
  const messageData = encoder.encode(message);
  
  const cryptoKey = await crypto.subtle.importKey(
    'raw',
    keyData,
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );
  
  const signature = await crypto.subtle.sign(
    'HMAC',
    cryptoKey,
    messageData
  );
  
  // Convert to hex string
  return Array.from(new Uint8Array(signature))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
}