import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import {Link} from '@/i18n/navigation';
import { CheckCircle, Home, Edit, Heart, User, HelpCircle } from "lucide-react"
import { useTranslations } from "next-intl"

export default function WelcomeNewUser() {
  const t = useTranslations('WelcomeNewUser');

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-emerald-50 via-white to-blue-50 p-4">
      <Card className="max-w-2xl w-full shadow-xl border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader className="text-center pb-4">
          <div className="relative w-24 h-24 mx-auto mb-4">
            <div className="absolute inset-0 bg-emerald-100 rounded-full flex items-center justify-center">
              <CheckCircle className="h-12 w-12 text-emerald-600" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-emerald-700 mb-2">{t('title')}</h1>
          <p className="text-lg text-emerald-600 font-medium">{t('subtitle')}</p>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Welcome Message */}
          <div className="bg-emerald-50 p-4 rounded-lg border border-emerald-200">
            <p className="text-gray-700 leading-relaxed">
              {t('message')}
            </p>
          </div>

          {/* Getting Started Steps */}
          <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h3 className="font-semibold text-gray-800 mb-4">{t('gettingStartedTitle')}</h3>
            <div className="space-y-3">
              <div className="flex items-start gap-3 text-gray-700">
                <Home className="h-5 w-5 text-emerald-500 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="font-medium">{t('step1Title')}</p>
                  <p className="text-sm text-gray-600">{t('step1Desc')}</p>
                </div>
              </div>
              <div className="flex items-start gap-3 text-gray-700">
                <User className="h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="font-medium">{t('step2Title')}</p>
                  <p className="text-sm text-gray-600">{t('step2Desc')}</p>
                </div>
              </div>
              <div className="flex items-start gap-3 text-gray-700">
                <Edit className="h-5 w-5 text-purple-500 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="font-medium">{t('step3Title')}</p>
                  <p className="text-sm text-gray-600">{t('step3Desc')}</p>
                </div>
              </div>
              <div className="flex items-start gap-3 text-gray-700">
                <Heart className="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="font-medium">{t('step4Title')}</p>
                  <p className="text-sm text-gray-600">{t('step4Desc')}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Action Buttons */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <Button asChild className="bg-emerald-600 hover:bg-emerald-700 text-white font-medium">
              <Link href="/">{t('exploreButton')}</Link>
            </Button>
            <Button asChild variant="outline" className="border-emerald-300 text-emerald-700 hover:bg-emerald-50">
              <Link href="/user/profile">{t('profileButton')}</Link>
            </Button>
            <Button asChild variant="outline" className="border-purple-300 text-purple-700 hover:bg-purple-50">
              <Link href="/user/bds/new">{t('postPropertyButton')}</Link>
            </Button>
            <Button asChild variant="outline" className="border-red-300 text-red-700 hover:bg-red-50">
              <Link href="/user/favorite">{t('favoritesButton')}</Link>
            </Button>
          </div>

          {/* Support Information */}
          <div className="text-center pt-4 border-t border-gray-200">
            <div className="flex items-center justify-center gap-2 mb-2">
              <HelpCircle className="h-5 w-5 text-gray-500" />
              <p className="text-sm text-gray-600 font-medium">{t('supportTitle')}</p>
            </div>
            <p className="text-sm text-gray-600">{t('supportText')}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
