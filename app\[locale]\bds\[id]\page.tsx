import { Metadata } from "next";
import { notFound } from "next/navigation";
import { getPropertyById } from "@/app/actions/server/property";
import PropertyDetailWrapper from "./PropertyDetailWrapper";

interface PropertyDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

export async function generateMetadata({ params }: PropertyDetailPageProps): Promise<Metadata> {
  const property = await getPropertyById((await params).id);

  if (!property) {
    return {
      title: "Property Not Found | YEZ Home",
      description: "The requested property could not be found.",
    };
  }

  return {
    title: `${property.name} | YEZ Home`,
    description: property.description || `View details about ${property.name} on YEZ Home`,
  };
}

// This is a server component that fetches the data and passes it to the client component
export default async function PropertyDetailPage({ params }: PropertyDetailPageProps) {
  const property = await getPropertyById((await params).id);
  if (!property || property.success === false) {
    notFound();
  }

  return <PropertyDetailWrapper property={property.data} />;
}
