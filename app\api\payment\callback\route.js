import { NextResponse } from "next/server";
import { verifyMomoPayment } from "@/app/services/payment";

export async function POST(request) {
  try {
    const body = await request.json();
    
    // Determine which payment provider sent the callback
    let verificationResult;
    
    if (body.partnerCode) {
      // MoMo callback
      verificationResult = await verifyMomoPayment(body.orderId);
    } else {
      // Unknown provider
      return NextResponse.json(
        { success: false, message: "Unknown payment provider" },
        { status: 400 }
      );
    }
    
    if (verificationresult?.isSuccess && verificationResult.verified) {
      // Update the transaction in your database
      // This would call your backend API
      const updateResult = await fetch(`${process.env.API_URL}/api/user/wallet/transactions/${body.orderId}/complete`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${process.env.INTERNAL_API_KEY}`, // Use a secure API key for server-to-server communication
        },
        body: JSON.stringify({
          amount: verificationResult.amount,
          transactionId: verificationResult.transactionId,
        }),
      });
      
      const result = await updateResult.json();
      
      if (result?.isSuccess) {
        // Return success to the payment provider
        return NextResponse.json({ success: true });
      } else {
        console.error("Failed to update transaction:", result);
        return NextResponse.json(
          { success: false, message: "Failed to update transaction" },
          { status: 500 }
        );
      }
    } else {
      console.error("Payment verification failed:", verificationResult);
      return NextResponse.json(
        { success: false, message: "Payment verification failed" },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("Payment callback error:", error);
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
} 