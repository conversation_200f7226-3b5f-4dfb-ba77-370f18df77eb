export default function VnPayIcon({ className }: { className?: string }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none" className={className}><path d="M24.3074 22.4922H22.4908V24.3088H24.3074V22.4922Z" fill="#999999"></path><path d="M18.8578 17.042H17.0413V18.8586H18.8578V17.042Z" fill="#999999"></path><path d="M20.6744 18.8584H18.8578V20.675H20.6744V18.8584Z" fill="#999999"></path><path d="M18.8577 20.6748H17.0411V22.4914H18.8577V20.6748Z" fill="#999999"></path><path d="M20.6744 22.4922H18.8578V24.3088H20.6744V22.4922Z" fill="#999999"></path><path d="M22.491 20.6748H20.6744V22.4914H22.491V20.6748Z" fill="#999999"></path><path d="M22.491 17.042H20.6744V18.8586H22.491V17.042Z" fill="#999999"></path><path d="M24.3074 18.8584H22.4908V20.675H24.3074V18.8584Z" fill="#999999"></path><rect x="8.61536" y="8.61523" width="5.53846" height="5.53846" stroke="#999999" strokeWidth="1.7" strokeLinejoin="round"></rect><rect x="8.61523" y="17.8457" width="5.53846" height="5.53846" stroke="#999999" strokeWidth="1.7" strokeLinejoin="round"></rect><rect x="17.8461" y="8.61523" width="5.53846" height="5.53846" stroke="#999999" strokeWidth="1.7" strokeLinejoin="round"></rect><path d="M10.4615 4H4V10.4615" stroke="#CCCCCC" strokeWidth="1.7" strokeLinecap="round" strokeLinejoin="round"></path><path d="M21.5385 28H28V21.5385" stroke="#CCCCCC" strokeWidth="1.7" strokeLinecap="round" strokeLinejoin="round"></path><path d="M21.5385 4H28V10.4615" stroke="#CCCCCC" strokeWidth="1.7" strokeLinecap="round" strokeLinejoin="round"></path><path d="M10.4615 28H4V21.5385" stroke="#CCCCCC" strokeWidth="1.7" strokeLinecap="round" strokeLinejoin="round"></path></svg>
  )
}
