"use client";

import { useEffect, useState, useCallback } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON>le, DialogFooter, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { getContactRequestsByPropertyId, updateContactRequest } from "@/app/actions/server/contactRequest";
import { useToast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { Calendar, Check, CheckCircle2, Mail, MessageSquare, Phone } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";
import { Separator } from "@/components/ui/separator";
import { LoadingSpinner } from "../ui/loading-spinner";

export default function ContactRequestModal({ propertyId, open, onClose }) {
  const { toast } = useToast();
  const [contactRequests, setContactRequests] = useState([]);
  const [selectedRequests, setSelectedRequests] = useState([]);
  const [loading, setLoading] = useState(false);
  const t = useTranslations("ContactRequestModal");
  useEffect(() => {
    if (open && propertyId) {
      loadContactRequests();
    } else {
      setSelectedRequests([]);
    }
  }, [open, propertyId]);

  const loadContactRequests = async () => {
    setLoading(true);
    try {
      const response = await getContactRequestsByPropertyId(propertyId);
      if (response?.isSuccess) {
        setContactRequests(response.data);
      } else {
        toast({
          title: t("error"),
          description: response.message || t("cannotLoadContactRequestList"),
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error(t("errorLoadingContactRequests"), error);
    } finally {
      setLoading(false);
    }
  };

  const handleSelectAll = useCallback(
    (checked) => {
      if (checked) {
        setSelectedRequests(contactRequests.map((request) => request.id));
      } else {
        setSelectedRequests([]);
      }
    },
    [contactRequests]
  );

  const handleSelectRequest = useCallback((requestId, checked) => {
    setSelectedRequests((prev) => {
      if (checked) {
        return [...prev, requestId];
      } else {
        return prev.filter((id) => id !== requestId);
      }
    });
  }, []);

  const handleMarkAsRead = async (requestIds) => {
    setLoading(true);
    try {
      const formData = new FormData();
      const id = Array.isArray(requestIds) ? requestIds[0] : requestIds;
      formData.append("id", id);
      formData.append("status", "read");
      formData.append("note", "Đã đọc");

      const response = await updateContactRequest(null, formData);
      if (response?.isSuccess) {
        setContactRequests((prevRequests) =>
          prevRequests.map((request) => (request.id === id ? { ...request, status: "read", note: "Đã đọc" } : request))
        );

        toast({
          title: "Thành công",
          description: "Đã đánh dấu đã đọc",
          className: "bg-teal-600 text-white",
        });
        setSelectedRequests((prev) => prev.filter((selectedId) => selectedId !== id));
      } else {
        toast({
          title: t("error"),
          description: response.message || "Không thể cập nhật trạng thái",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error(t("errorMarkingAsRead"), error);
    } finally {
      setLoading(false);
    }
  };

  const handleBulkMarkAsRead = async () => {
    if (selectedRequests.length === 0) {
      toast({
        title: t("error"),
        description: t("pleaseSelectAtLeastOneRequest"),
        variant: "default",
      });
      return;
    }

    setLoading(true);
    try {
      const results = await Promise.all(
        selectedRequests.map(async (id) => {
          const formData = new FormData();
          formData.append("id", id);
          formData.append("status", "read");
          formData.append("note", t("read"));
          return await updateContactRequest(null, formData);
        })
      );

      const allSuccessful = results.every((result) => result?.isSuccess);

      if (allSuccessful) {
        setContactRequests((prevRequests) =>
          prevRequests.map((request) => (selectedRequests.includes(request.id) ? { ...request, status: "read", note: t("read") } : request))
        );

        toast({
          title: t("success"),
          description: t("allRequestsMarkedAsRead"),
          className: "bg-teal-600 text-white",
        });
        setSelectedRequests([]);
      } else {
        toast({
          title: t("error"),
          description: t("cannotUpdateSomeRequests"),
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error(t("errorInBulkMarkAsRead"), error);
      toast({
        title: t("error"),
        description: t("errorOccurredWhenUpdating"),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const isAllSelected = contactRequests.length > 0 && selectedRequests.length === contactRequests.length;
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] p-0 gap-0" onPointerDownOutside={(e) => e.preventDefault()}>
        <DialogHeader className="rounded-t-lg p-6 border-b bg-gradient-to-r from-blue-50 to-indigo-50">
          <DialogTitle className="text-2xl font-bold text-gray-900 mb-3">{t("contactRequestList")}</DialogTitle>
          <DialogDescription className="text-sm text-gray-600">
            {contactRequests.filter((c) => c.status !== "read").length} yêu cầu chưa đọc
          </DialogDescription>
        </DialogHeader>

        {/* Controls */}
        <div className="px-6 py-3 border-b bg-gray-50/50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Checkbox id="selectAll" checked={isAllSelected} onCheckedChange={handleSelectAll} disabled={loading || contactRequests.length === 0} />
              <label htmlFor="selectAll" className="text-sm font-medium text-gray-700">
                {t("selectAll")}
              </label>
              {selectedRequests.length > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {selectedRequests.length} đã chọn
                </Badge>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                className="ml-auto"
                onClick={handleBulkMarkAsRead}
                disabled={loading || selectedRequests.length === 0}
              >
                <CheckCircle2 className="h-4 w-4 mr-2" />
                {t("markAsRead")}
              </Button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="overflow-y-auto max-h-[50vh] p-6">
          <div className="space-y-4">
            {loading && (
              <div className="absolute inset-0 z-10 bg-white/70 flex items-center justify-center">
                <LoadingSpinner text="Đang tải dữ liệu..." size="md" />
              </div>
            )}
            {contactRequests.map((request) => (
              <Card
                key={request.id}
                className={`transition-all duration-200 hover:shadow-md ${
                  request.status === "read" ? "border-l-4 border-l-blue-500 bg-blue-50/30" : "border-l-4 border-l-gray-200"
                }`}
              >
                <CardContent className="p-3">
                  <div className="flex items-start space-x-4">
                    <Checkbox
                      checked={selectedRequests.includes(request.id)}
                      onCheckedChange={(checked) => handleSelectRequest(request.id, checked)}
                      disabled={loading}
                      className="mt-1"
                    />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <h5 className="text-lg font-semibold text-gray-900">{request.name}</h5>
                          {request.status !== "read" && <Badge className="bg-red-100 text-red-700 hover:bg-red-200">Chưa đọc</Badge>}
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline" className="text-xs text-gray-500">
                            <Calendar className="h-3 w-3 mr-1" />
                            {format(new Date(request.sentAt), "dd/MM/yyyy HH:mm")}
                          </Badge>
                          {request.status !== "read" && (
                            <Button variant="ghost" size="sm" onClick={() => handleMarkAsRead(request.id)} disabled={loading} className="shrink-0">
                              <Check className="h-3 w-3 mr-1" />
                            </Button>
                          )}
                        </div>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <Mail className="h-4 w-4 text-blue-500" />
                          <span className="font-medium">Email:</span>
                          <a href={`mailto:${request.email}`} className="text-blue-600 hover:underline">
                            {request.email}
                          </a>
                        </div>
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <Phone className="h-4 w-4 text-green-500" />
                          <span className="font-medium">Điện thoại:</span>
                          <a href={`tel:${request.phone}`} className="text-green-600 hover:underline">
                            {request.phone}
                          </a>
                        </div>
                      </div>

                      <Separator className="my-3" />
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2 text-sm font-medium text-gray-700">
                          <MessageSquare className="h-4 w-4 text-purple-500" />
                          <span>Nội dung:</span>
                        </div>
                        {request.note && (
                          <p
                            className={cn(
                              "text-sm text-gray-600 leading-relaxed pl-6 bg-gray-50 p-3 rounded-lg",
                              request.status === "read" && "text-gray-500"
                            )}
                          >
                            {request.note}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
            {contactRequests.length === 0 && <div className="text-center text-gray-500 py-8">{t("noContactRequest")}</div>}
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            {t("close")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
