"use client";

import { Separator } from "@/components/ui/separator";
import PropertyImageUploader from "./PropertyImageUploader";
import PropertyVideoInput from "./PropertyVideoInput";
import { useTranslations } from "next-intl";
import { CollapsibleSection } from "../ui/collapsible-section";
import { Card } from "../ui/card";
import { Alert, AlertDescription } from "../ui/alert";
import { AlertCircleIcon } from "lucide-react";

const PropertyMediaSection = ({ form, property, uploadedFiles, setUploadedFiles, isFormDisabled }) => {
  const t = useTranslations("PropertyForm");

  const onUploadComplete = (files) => {
    setUploadedFiles(files);
  };

  return (
    <Card className="border hover:border-blue-200 transition-all duration-300 mb-3">
      <CollapsibleSection title={t("mediaSection")} subTitle={t("requiredInfo")} defaultOpen={true}>
        <Separator className="mb-6" />
        <Alert className="mb-2 bg-teal-50 border-teal-400">
          <AlertDescription className="mb-0 flex gap-2 items-center">
            <AlertCircleIcon className="h-4 w-4" />
            {t("avatarIconDescription")}
          </AlertDescription>
        </Alert>
        <PropertyImageUploader
          propertyId={property?.id}
          initialImages={uploadedFiles}
          onUploadComplete={onUploadComplete}
          isFormDisabled={isFormDisabled}
        />
        <div className="mt-6">
          <PropertyVideoInput form={form} isFormDisabled={isFormDisabled} />
        </div>
      </CollapsibleSection>
    </Card>
  );
};

export default PropertyMediaSection;
