"use client";

import { useEffect, useRef, useState } from "react";

export default function PropertyMapSection({ latitude, longitude, address }) {
  const mapContainerRef = useRef(null);
  const [map, setMap] = useState(null);
  const [loading, setLoading] = useState(true);
  const mapInstanceRef = useRef(null);

  // Initialize map on component mount
  useEffect(() => {
    if (!mapContainerRef.current) return;

    // Load Goong Maps script
    const script = document.createElement("script");
    script.src = "https://cdn.jsdelivr.net/npm/@goongmaps/goong-js@1.0.9/dist/goong-js.js";
    script.async = true;
    document.body.appendChild(script);

    // Load CSS
    const link = document.createElement("link");
    link.href = "https://cdn.jsdelivr.net/npm/@goongmaps/goong-js@1.0.9/dist/goong-js.css";
    link.rel = "stylesheet";
    document.head.appendChild(link);

    let mapInstance = null;

    script.onload = () => {
      if (!window.goongjs) {
        console.error("Goong Maps failed to load");
        setLoading(false);
        return;
      }

      window.goongjs.accessToken = process.env.NEXT_PUBLIC_GOONG_MAPTILES_KEY || '8qzxZAuxcsctSlmOszInchP1A5GrmRBHJwCBCjO6'; // Use your Goong Maps API key

      mapInstance = new window.goongjs.Map({
        container: mapContainerRef.current,
        style: 'https://tiles.goong.io/assets/goong_map_web.json',
        center: [longitude, latitude],
        zoom: 15
      });

      mapInstanceRef.current = mapInstance;

      mapInstance.on('load', () => {
        setMap(mapInstance);
        setLoading(false);
        
        // Add property marker
        addPropertyMarker(mapInstance);
      });
    };

    return () => {
      // Clean up
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
      if (document.body.contains(script)) {
        document.body.removeChild(script);
      }
      if (document.head.contains(link)) {
        document.head.removeChild(link);
      }
    };
  }, [latitude, longitude]);

  // Function to add property marker
  const addPropertyMarker = (mapInstance) => {
    if (!mapInstance) return;
    
    // Create marker element
    const el = document.createElement('div');
    el.className = 'property-marker';
    el.style.width = '32px';
    el.style.height = '32px';
    el.style.backgroundImage = 'url(/marker.svg)';
    el.style.backgroundSize = 'cover';
    
    // Create popup with property information
    const popupContent = `
      <div style="max-width: 200px;">
        <p style="font-weight: bold; margin-bottom: 5px;">${address}</p>
      </div>
    `;
    
    // Create popup
    const popup = new window.goongjs.Popup({ 
      offset: 25,
      closeButton: true,
      closeOnClick: false
    }).setHTML(popupContent);
    
    // Create marker
    new window.goongjs.Marker(el)
      .setLngLat([longitude, latitude])
      .setPopup(popup)
      .addTo(mapInstance);
  };

  return (
    <div className="relative h-[400px] w-full rounded-lg overflow-hidden">
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 z-10">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      )}
      <div 
        ref={mapContainerRef} 
        className="h-full w-full"
      />
      <div className="absolute bottom-4 left-4 right-4 bg-white p-3 rounded-lg shadow-md">
        <p className="text-sm text-gray-600">{address}</p>
      </div>
      
      {/* Add custom CSS for markers */}
      <style jsx global>{`
        .property-marker {
          background-size: cover;
          transition: all 0.3s ease;
          cursor: pointer;
        }
      `}</style>
    </div>
  );
} 