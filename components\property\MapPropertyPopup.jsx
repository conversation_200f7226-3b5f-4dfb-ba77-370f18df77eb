"use client";

import Image from "next/image";
import { ExternalLinkIcon } from "lucide-react";
import { formatCurrency } from "@/lib/utils";

export const MapPropertyPopup = ({ tCommon, property, onViewDetails }) => {

  const handleViewDetails = (e) => {
    e.stopPropagation();
    if (onViewDetails) {
      onViewDetails(property);
    }
  };

  return (
    <div className="w-[180px] xs:w-[220px] sm:w-64 bg-white rounded-lg overflow-hidden shadow-lg">
      {/* Image Container */}
      <div className="relative h-24 xs:h-28 sm:h-32 w-full overflow-hidden">
        <Image
          src={property.imageUrl || "/placeholder.svg?height=128&width=256"}
          alt={property.name}
          fill
          className="object-cover"
          sizes="256px"
        />

        {/* Sale Badge */}
        <div className="absolute top-2 left-2 bg-linear-to-r from-red-500 to-rose-500 text-white px-2 py-0.5 rounded-full text-xs font-medium">
          {tCommon(`propertyPostType_${property.postType}`)}
        </div>

        {/* Highlight Badge */}
        {property.isHighlighted && (
          <div className="absolute top-2 right-2 bg-black/60 text-white px-1.5 py-0.5 rounded text-[10px] font-medium">
            {tCommon(`highlight_status`)}
          </div>
        )}
      </div>

      {/* Property Details */}
      <div className="p-3">
        {/* Price and Type */}
        <div className="flex justify-between items-center mb-1">
          <div className="text-sm xs:text-base font-bold text-gray-800">
            {formatCurrency(property.price)}
          </div>
          <div className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded">
            {property.propertyType}
          </div>
        </div>

        {/* Title */}
        <h3 className="text-sm font-medium text-gray-800 mb-1 line-clamp-2">{property.name}</h3>

        <div className="flex justify-between gap-1 text-xs text-gray-500 mb-3">
          <span>{property.area || "__"} m²</span>
          <span>{property.rooms || "__"} PN</span>
          <span>{property.toilets || "__"} PT</span>
        </div>

        {/* View Details Button */}
        <button
          onClick={handleViewDetails}
          className="w-full bg-linear-to-r from-blue-500 to-teal-500 text-white py-1.5 sm:py-2 rounded text-xs font-medium hover:opacity-90 transition-opacity flex items-center justify-center"
        >
          Xem chi tiết
          <ExternalLinkIcon size={12} className="ml-1" />
        </button>
      </div>
    </div>
  );
};
