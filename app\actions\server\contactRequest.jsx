"use server";

import { handleErrorResponse, logError } from "@/lib/apiUtils";
import { fetchWithAuth, fetchWithoutAuth, getSession } from "@/lib/sessionUtils";
import { parseEmptyStringsToNull } from "@/lib/utils";

const API_BASE_URL = `${process.env.API_URL}/api/ContactRequest`;

/**
 * Get all contact requests for a specific property
 * @param {string} propertyId - The ID of the property
 */
export async function getContactRequestsByPropertyId(propertyId) {
  const response = await fetchWithAuth(`${API_BASE_URL}/property/${propertyId}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });

  return response;
}

/**
 * Get a specific contact request by ID
 * @param {string} id - The ID of the contact request
 */
export async function getContactRequestById(id) {
  const response = await fetchWithAuth(`${API_BASE_URL}/${id}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });
  return response;
}

/**
 * Create a new contact request
 * @param {Object} prevState - Previous state
 * @param {FormData} formData - Form data containing contact request details
 */
export async function createContactRequest(prevState, formData) {
  // Convert FormData to a plain object for easier handling
  const formDataObject = Object.fromEntries(formData.entries());

  const payload = {
    ...formDataObject,
  };

  // Get the current user's ID if they're logged in
  const userSession = await getSession("User");
  if (userSession) {
    const user = JSON.parse(userSession);
    payload.userId = user.id;
  }

  return await fetchWithoutAuth(API_BASE_URL, {
    method: "POST",
    body: JSON.stringify(parseEmptyStringsToNull(payload)),
    headers: {
      "Content-Type": "application/json",
    },
  });
}

/**
 * Update a contact request
 * @param {Object} prevState - Previous state
 * @param {FormData} formData - Form data containing updated contact request details
 */
export async function updateContactRequest(prevState, formData) {
  const id = formData.get("id");
  if (!id) {
    return handleErrorResponse(false, null, "ID yêu cầu liên hệ không hợp lệ");
  }

  // Convert FormData to a plain object for easier handling
  const formDataObject = Object.fromEntries(formData.entries());

  const payload = {
    ...formDataObject,
  };

  return await fetchWithAuth(`${API_BASE_URL}/${id}`, {
    method: "PUT",
    body: JSON.stringify(parseEmptyStringsToNull(payload)),
    headers: {
      "Content-Type": "application/json",
    },
  });
}

/**
 * Delete a contact request
 * @param {string} id - The ID of the contact request to delete
 */
export async function deleteContactRequest(id) {
  return await fetchWithAuth(`${API_BASE_URL}/${id}`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
    },
  });
}
