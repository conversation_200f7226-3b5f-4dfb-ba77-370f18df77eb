"use client";

import React, { useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { ArrowLeft, OctagonX } from "lucide-react";
import { Link } from "@/i18n/navigation";
import { FormType, CAN_NOT_EDIT_STATUS, PropertyStatus } from "@/lib/enum";
import { useTranslations } from "next-intl";

// Memoize the component to prevent unnecessary re-renders
const PropertySaveButtons = React.memo(({
  formType,
  propertyStatus,
  isPending,
  formHandleSubmit,
  onSubmit
}) => {
  const t = useTranslations("PropertyForm");

  // Memoize the handler for saving as draft
  const handleSaveDraft = useCallback(() => {
    return formHandleSubmit((data) => onSubmit(data, "saveDraft"))();
  }, [formHandleSubmit, onSubmit]);

  if (formType === FormType.EDIT && CAN_NOT_EDIT_STATUS.includes(propertyStatus)) {
    return (
      <div className="flex flex-col items-center justify-center">
        <Alert className="bg-yellow-100">
          <OctagonX className="h-4 w-4" />
          <AlertDescription>{t("propertyIsBeingReviewedOrApprovedOrSold")}</AlertDescription>
        </Alert>
        <Link href="/user/bds" className="mt-3 flex items-center gap-2 text-sm text-gray-500 hover:text-gray-700">
          <ArrowLeft className="h-4 w-4" /> {t("backToPropertyList")}
        </Link>
      </div>
    );
  } else {
    return (
      <div className="flex gap-4 items-center justify-center">
        <Button
          type="button"
          className="py-4 bg-gray-500 hover:bg-gray-600"
          onClick={handleSaveDraft}
          disabled={isPending}
        >
          {isPending ? t("processing") : t("saveDraft")}
        </Button>

        <Button type="submit" className="py-4 bg-teal-600 hover:bg-teal-700" disabled={isPending}>
          {isPending ? t("processing") : t("saveAndSendForApproval")}
        </Button>
      </div>
    );
  }
}, (prevProps, nextProps) => {
  // Only re-render if these props change
  return (
    prevProps.formType === nextProps.formType &&
    prevProps.propertyStatus === nextProps.propertyStatus &&
    prevProps.isPending === nextProps.isPending
    // We don't compare formHandleSubmit and onSubmit as they should be memoized by the parent
  );
});

export default PropertySaveButtons;
