import { getPropertyById } from "@/app/actions/server/property";
import { FormType } from "@/lib/enum";
import PropertyForm from "../new/PropertyForm";
import { OctagonX } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { getTranslations } from 'next-intl/server';

export default async function PropertyDetailPage({ params }) {
  const t = await getTranslations('PropertyForm');
  const propertyId = (await params).propertyId;
  const propertyResponse = await getPropertyById(propertyId);

  if (!propertyResponse?.isSuccess) {
    return (
      <div className="inset-0 z-0 bg-slate-50 px-4 md:px-16 py-4">
        <div className="container">
          <Alert variant="destructive">
            <OctagonX className="h-4 w-4" />
            <AlertTitle>{t('fetchPropertyErrorTitle')}</AlertTitle>
            <AlertDescription>
              {t('fetchPropertyErrorDescription')}
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return <PropertyForm property={propertyResponse.data} formType={FormType.EDIT}></PropertyForm>;
}
