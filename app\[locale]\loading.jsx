import Image from "next/image"
import { Loader2 } from "lucide-react"
import { getTranslations } from 'next-intl/server'

export default async function Loading() {
  const t = await getTranslations('Loading')

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-white">
      <div className="mb-8">
        <Image src="/yezhome_logo.png" alt="YEZHOME Logo" width={150} height={150} priority />
      </div>
      <p className="text-2xl font-semibold mb-4 text-gray-800">{t('message')}</p>
      <Loader2 className="h-8 w-8 animate-spin text-primary" />
    </div>
  )
}

