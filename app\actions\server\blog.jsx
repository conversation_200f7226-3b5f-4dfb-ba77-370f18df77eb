"use server";

import { fetchWithoutAuth } from "@/lib/sessionUtils";

const API_BASE_URL = `${process.env.API_URL}/api/Blog`;

export async function getBlogBySlug(slug) {
  const response = await fetchWithoutAuth(`${API_BASE_URL}/slug/${slug}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });
  return response;
}

export async function getAllBlog() {
  const response = await fetchWithoutAuth(API_BASE_URL, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });
  return response;
}

export async function getBlogPosts({ PageNumber = 1, PageSize = 30, SortColumn = "publishedAt", SortDescending = true, title = "" }) {
    const queryParams = new URLSearchParams({
      PageNumber,
      PageSize,
      SortColumn,
      SortDescending,
      title,
    }).toString();

    const response = await fetchWithoutAuth(`${API_BASE_URL}/blog-posts?${queryParams}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    return response;
}
