"use client";

import { useAuth } from "@/contexts/AuthContext";
import { useAlert } from "@/contexts/AlertContext";
import { Mail, Phone, RotateCw, User, Building, AlertTriangle, Trash2, Save, X, ChevronDown, Upload, Camera } from "lucide-react";
import { useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import {
  getUserTaxInfo,
  updateUserTaxInfo,
  deactivateUserAccount,
  requestAccountDeletion,
  uploadAvatar,
  deleteAvatar,
} from "@/app/actions/server/user";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Dialog, DialogContent, DialogDescription, DialogFooter, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import ChangePassword from "./ChangePassword";
import Image from "next/image";
import { logout } from "@/app/actions/server/authenticate";

export default function ProfileCard() {
  const t = useTranslations("UserProfilePage");
  const { profile, loading, refreshProfile } = useAuth();
  const [taxInfo, setTaxInfo] = useState(null);
  const [taxInfoLoading, setTaxInfoLoading] = useState(true);
  const [isEditingTaxInfo, setIsEditingTaxInfo] = useState(false);
  const [taxFormData, setTaxFormData] = useState({});
  const [errors, setErrors] = useState({});
  const [deactivateDialogOpen, setDeactivateDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [password, setPassword] = useState("");
  const [reason, setReason] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [avatarFile, setAvatarFile] = useState(null);
  const [avatarPreview, setAvatarPreview] = useState(null);
  const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);
  const { showAlert } = useAlert();
  const { toast } = useToast();

  // Fetch tax info when component mounts
  useEffect(() => {
    const fetchTaxInfo = async () => {
      setTaxInfoLoading(true);
      try {
        const response = await getUserTaxInfo();
        if (response && response?.isSuccess) {
          setTaxInfo(response?.data);
          setTaxFormData(response?.data || {});
        } else {
          showAlert(response, null, null);
        }
      } catch (error) {
        console.error("Error fetching tax info:", error);
        showAlert({ success: false, message: t("taxInfoLoadingError") }, null, null);
      } finally {
        setTaxInfoLoading(false);
      }
    };

    fetchTaxInfo();
  }, [showAlert, t]);

  const handleTaxInfoChange = (e) => {
    const { name, value } = e.target;
    setTaxFormData((prev) => {
      // Handle nested properties for invoiceInfo
      if (name.includes(".")) {
        const [parent, child] = name.split(".");
        return {
          ...prev,
          [parent]: {
            ...prev[parent],
            [child]: value,
          },
        };
      }
      return { ...prev, [name]: value };
    });

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: undefined }));
    }
  };

  const validateTaxCode = (value) => {
    if (!value || !value.trim()) {
      return t("personalTaxCodeError");
    }
    const taxCodeRegex = /^\d{10}(-\d{3})?$/;
    if (!taxCodeRegex.test(value)) {
      return t("personalTaxCodeInvalid");
    }
    return undefined;
  };

  // Email validation function
  const validateEmail = (value) => {
    if (!value || !value.trim()) {
      return t("invoiceInfoEmailError");
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(value)) {
      return t("invoiceInfoEmailInvalid");
    }
    return undefined;
  };

  // Generic required field validation
  const validateRequired = (value, fieldName) => {
    if (!value || !value.trim()) {
      return t(`${fieldName}Error`);
    }
    return undefined;
  };

  // Handle input blur for immediate validation
  const handleInputBlur = (fieldName) => {
    let value;
    let error;

    // Get value based on field structure
    if (fieldName.includes(".")) {
      const [parent, child] = fieldName.split(".");
      value = taxFormData[parent][child];
    } else {
      value = taxFormData[fieldName];
    }

    switch (fieldName) {
      case "personalTaxCode":
      case "invoiceInfo.taxCode":
        error = validateTaxCode(value);
        break;
      case "invoiceInfo.email":
        error = validateEmail(value);
        break;
      case "invoiceInfo.buyerName":
        error = validateRequired(value, "invoiceInfoBuyerName");
        break;
      case "invoiceInfo.companyName":
        error = validateRequired(value, "invoiceInfoCompanyName");
        break;
      case "invoiceInfo.address":
        error = validateRequired(value, "invoiceInfoAddress");
        break;
    }

    if (error) {
      setErrors((prev) => ({ ...prev, [fieldName]: error }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    newErrors.personalTaxCode = validateTaxCode(taxFormData.personalTaxCode);
    newErrors["invoiceInfo.buyerName"] = validateRequired(taxFormData.invoiceInfo.buyerName, "invoiceInfoBuyerName");
    newErrors["invoiceInfo.email"] = validateEmail(taxFormData.invoiceInfo.email);
    newErrors["invoiceInfo.companyName"] = validateRequired(taxFormData.invoiceInfo.companyName, "invoiceInfoCompanyName");
    newErrors["invoiceInfo.taxCode"] = validateTaxCode(taxFormData.invoiceInfo.taxCode);
    newErrors["invoiceInfo.address"] = validateRequired(taxFormData.invoiceInfo.address, "invoiceInfoAddress");

    // Remove undefined errors
    Object.keys(newErrors).forEach((key) => {
      if (!newErrors[key]) {
        delete newErrors[key];
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleTaxInfoSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) {
      toast({
        description: t("taxInfoUpdateError"),
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await updateUserTaxInfo(taxFormData);
      if (response && response?.isSuccess) {
        setTaxInfo(taxFormData);
        setIsEditingTaxInfo(false);
        toast({
          description: t("taxInfoUpdateSuccess"),
          className: "bg-teal-600 text-white",
        });
      } else {
        showAlert(response, null, null);
      }
    } catch (error) {
      console.error("Error updating tax info:", error);
      showAlert({ success: false, message: t("taxInfoUpdateError") }, null, null);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeactivateAccount = async () => {
    if (!password) {
      toast({
        description: t("passwordRequired"),
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await deactivateUserAccount({ password, reason });
      if (response && response?.isSuccess) {
        showAlert({
          title: t("deactivateAccountButton"),
          message: t("accountDeactivateSuccess"),
          hasCancel: false,
          onConfirm: async () => {
            await logout();
          },
        });
      } else {
        showAlert(response, null, null);
      }
    } catch (error) {
      console.error("Error deactivating account:", error);
      showAlert({ success: false, message: t("accountDeactivateError") }, null, null);
    } finally {
      setIsSubmitting(false);
      setDeactivateDialogOpen(false);
    }
  };

  const handleDeleteAccount = async () => {
    if (!password) {
      toast({
        description: t("passwordRequired"),
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await requestAccountDeletion({ password, reason });
      if (response && response?.isSuccess) {
        showAlert({
          title: t("deleteAccountButton"),
          message: t("accountDeleteRequestSuccess"),
          hasCancel: false,
          onConfirm: async () => {
            await logout();
          },
        });
      } else {
        showAlert(response, null, null);
      }
    } catch (error) {
      console.error("Error requesting account deletion:", error);
      showAlert({ success: false, message: t("accountDeleteRequestError") }, null, null);
    } finally {
      setIsSubmitting(false);
      setDeleteDialogOpen(false);
    }
  };

  const handleAvatarChange = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Check file size (200KB = 200 * 1024 bytes)
    if (file.size > 200 * 1024) {
      toast({
        description: t("avatarSizeError"),
        variant: "destructive",
      });
      return;
    }

    setAvatarFile(file);
    const previewUrl = URL.createObjectURL(file);
    setAvatarPreview(previewUrl);
  };

  const handleAvatarUpload = async () => {
    if (!avatarFile) return;

    setIsUploadingAvatar(true);
    try {
      // If there's an existing avatar, delete it first
      if (profile?.user?.avatarURL) {
        await deleteAvatar();
      }

      const response = await uploadAvatar(avatarFile);
      if (response && response?.isSuccess) {
        toast({
          description: t("avatarUploadSuccess"),
          className: "bg-teal-600 text-white",
        });
        await refreshProfile();
      } else {
        showAlert(response, null, null);
      }
    } catch (error) {
      console.error("Error uploading avatar:", error);
      showAlert({ success: false, message: t("avatarUploadError") }, null, null);
    } finally {
      setIsUploadingAvatar(false);
      setAvatarFile(null);
      setAvatarPreview(null);
    }
  };

  const handleRemoveAvatar = async () => {
    setIsUploadingAvatar(true);
    try {
      const response = await deleteAvatar();
      if (response && response?.isSuccess) {
        toast({
          description: t("avatarDeleteSuccess"),
          className: "bg-teal-600 text-white",
        });
        await refreshProfile();
      } else {
        showAlert(response, null, null);
      }
    } catch (error) {
      console.error("Error deleting avatar:", error);
      showAlert({ success: false, message: t("avatarDeleteError") }, null, null);
    } finally {
      setIsUploadingAvatar(false);
    }
  };

  if (loading || taxInfoLoading) return <LoadingSpinner text={t("loadingMessage")} />;
  if (!profile) return <p>{t("userNotFound")}</p>;

  return (
    <Tabs defaultValue="personal" className="w-full">
      <TabsList className="grid w-full grid-cols-2">
        <TabsTrigger value="personal">Thông tin cá nhân</TabsTrigger>
        <TabsTrigger value="settings">Cài đặt tài khoản</TabsTrigger>
      </TabsList>

      <TabsContent value="personal">
        {/* Avatar Section */}
        <section className="mb-10 border-t border-gray-900/10 pt-12">
          <div className="space-y-4">
            <h3 className="text-base/7 font-semibold text-gray-900">{t("avatarTitle")}</h3>
            <hr className="mb-6 border-gray-200" />
            <div className="flex items-center space-x-6">
              <div className="relative h-24 w-24">
                {profile?.user?.avatarURL || avatarPreview ? (
                  <Image src={avatarPreview || profile?.user?.avatarURL} alt="Avatar" className="rounded-full object-cover" fill />
                ) : (
                  <div className="h-24 w-24 rounded-full bg-gray-200 flex items-center justify-center">
                    <Camera className="h-8 w-8 text-gray-400" />
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm" className="relative" disabled={isUploadingAvatar}>
                    <input
                      type="file"
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                      onChange={handleAvatarChange}
                      accept="image/*"
                    />
                    <Upload className="mr-2 h-4 w-4" />
                    {t("uploadAvatarButton")}
                  </Button>

                  {profile?.user?.avatarURL && (
                    <Button variant="outline" size="sm" onClick={handleRemoveAvatar} disabled={isUploadingAvatar}>
                      <X className="mr-2 h-4 w-4" />
                      {t("removeAvatarButton")}
                    </Button>
                  )}
                </div>

                {avatarFile && (
                  <Button size="sm" onClick={handleAvatarUpload} disabled={isUploadingAvatar}>
                    {isUploadingAvatar ? <RotateCw className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />}
                    {t("saveButton")}
                  </Button>
                )}

                <p className="text-sm text-gray-500">{t("avatarRequirements")}</p>
              </div>
            </div>
          </div>
        </section>

        {/* Account Information Section */}
        <section className="mb-10 border-t border-gray-900/10 pt-12">
          <div className="space-y-4">
            <h3 className="text-base/7 font-semibold text-gray-900">{t("contactInfoTitle")}</h3>
            <hr className="mb-6 border-gray-200" />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center space-x-4">
                <User className="h-6 w-6 text-gray-400" />
                <div>
                  <p className="text-sm font-medium text-gray-500">{t("fullNameLabel")}</p>
                  <p>{profile?.user?.fullName}</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <Mail className="h-6 w-6 text-gray-400" />
                <div>
                  <p className="text-sm font-medium text-gray-500">{t("emailLabel")}</p>
                  <p>{profile?.user?.email}</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <Phone className="h-6 w-6 text-gray-400" />
                <div>
                  <p className="text-sm font-medium text-gray-500">{t("phoneLabel")}</p>
                  <p>{profile?.user?.phone}</p>
                </div>
              </div>
              {profile?.user?.userType && (
                <div className="flex items-center space-x-4">
                  <Building className="h-6 w-6 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-500">{t("userTypeLabel")}</p>
                    <p>{profile?.user?.userType}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </section>

        {/* Tax and Invoice Information Section */}
        <section className="mb-10">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-base/7 font-semibold text-gray-900">{t("taxInfoTitle")}</h3>
            {!isEditingTaxInfo ? (
              <Button onClick={() => setIsEditingTaxInfo(true)} variant="outline">
                {t("editButton")}
              </Button>
            ) : (
              <Button onClick={() => setIsEditingTaxInfo(false)} variant="outline">
                <X className="mr-2 h-4 w-4" />
                {t("cancelButton")}
              </Button>
            )}
          </div>
          <hr className="mb-6 border-gray-200" />

          {isEditingTaxInfo ? (
            <form onSubmit={handleTaxInfoSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="personalTaxCode">{t("personalTaxCodeLabel")}</Label>
                  <Input
                    id="personalTaxCode"
                    name="personalTaxCode"
                    value={taxFormData.personalTaxCode || ""}
                    onChange={handleTaxInfoChange}
                    onBlur={() => handleInputBlur("personalTaxCode")}
                    placeholder={t("personalTaxCodePlaceholder")}
                  />
                  <span className="text-xs text-gray-500">{t("personalTaxCodeHelperText")}</span>
                  {errors["personalTaxCode"] && (
                    <p className="text-sm text-red-500" aria-live="polite">
                      {errors["personalTaxCode"]}
                    </p>
                  )}
                </div>
              </div>

              <h3 className="text-base/7 font-semibold text-gray-900 mt-6">{t("invoiceInfoTitle")}</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="invoiceInfo.buyerName">{t("buyerNameLabel")}</Label>
                  <Input
                    id="invoiceInfo.buyerName"
                    name="invoiceInfo.buyerName"
                    value={taxFormData.invoiceInfo?.buyerName || ""}
                    onChange={handleTaxInfoChange}
                    onBlur={() => handleInputBlur("invoiceInfo.buyerName")}
                    placeholder={t("buyerNamePlaceholder")}
                  />
                  {errors["invoiceInfo.buyerName"] && (
                    <p className="text-sm text-red-500" aria-live="polite">
                      {errors["invoiceInfo.buyerName"]}
                    </p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="invoiceInfo.email">{t("invoiceEmailLabel")}</Label>
                  <Input
                    id="invoiceInfo.email"
                    name="invoiceInfo.email"
                    type="email"
                    value={taxFormData.invoiceInfo?.email || ""}
                    onChange={handleTaxInfoChange}
                    onBlur={() => handleInputBlur("invoiceInfo.email")}
                    placeholder={t("invoiceEmailPlaceholder")}
                  />
                  {errors["invoiceInfo.email"] && (
                    <p className="text-sm text-red-500" aria-live="polite">
                      {errors["invoiceInfo.email"]}
                    </p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="invoiceInfo.companyName">{t("companyNameLabel")}</Label>
                  <Input
                    id="invoiceInfo.companyName"
                    name="invoiceInfo.companyName"
                    value={taxFormData.invoiceInfo?.companyName || ""}
                    onChange={handleTaxInfoChange}
                    onBlur={() => handleInputBlur("invoiceInfo.companyName")}
                    placeholder={t("companyNamePlaceholder")}
                  />
                  {errors["invoiceInfo.companyName"] && (
                    <p className="text-sm text-red-500" aria-live="polite">
                      {errors["invoiceInfo.companyName"]}
                    </p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="invoiceInfo.taxCode">{t("taxCodeLabel")}</Label>
                  <Input
                    id="invoiceInfo.taxCode"
                    name="invoiceInfo.taxCode"
                    value={taxFormData.invoiceInfo?.taxCode || ""}
                    onChange={handleTaxInfoChange}
                    onBlur={() => handleInputBlur("invoiceInfo.taxCode")}
                    placeholder={t("taxCodePlaceholder")}
                  />
                  <span className="text-xs text-gray-500">{t("personalTaxCodeHelperText")}</span>
                  {errors["invoiceInfo.taxCode"] && (
                    <p className="text-sm text-red-500" aria-live="polite">
                      {errors["invoiceInfo.taxCode"]}
                    </p>
                  )}
                </div>
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="invoiceInfo.address">{t("addressLabel")}</Label>
                  <Textarea
                    id="invoiceInfo.address"
                    name="invoiceInfo.address"
                    value={taxFormData.invoiceInfo?.address || ""}
                    onChange={handleTaxInfoChange}
                    onBlur={() => handleInputBlur("invoiceInfo.address")}
                    placeholder={t("addressPlaceholder")}
                    rows={3}
                  />
                  {errors["invoiceInfo.address"] && (
                    <p className="text-sm text-red-500" aria-live="polite">
                      {errors["invoiceInfo.address"]}
                    </p>
                  )}
                </div>
              </div>

              <div className="flex justify-end mt-6">
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? <RotateCw className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />}
                  {t("saveButton")}
                </Button>
              </div>
            </form>
          ) : (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">{t("personalTaxCodeLabel")}</p>
                  <p>{taxInfo?.personalTaxCode || t("notProvided")}</p>
                </div>
              </div>

              <h3 className="text-base/7 font-semibold text-gray-900 mt-6">{t("invoiceInfoTitle")}</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">{t("buyerNameLabel")}</p>
                  <p>{taxInfo?.invoiceInfo?.buyerName || t("notProvided")}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">{t("invoiceEmailLabel")}</p>
                  <p>{taxInfo?.invoiceInfo?.email || t("notProvided")}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">{t("companyNameLabel")}</p>
                  <p>{taxInfo?.invoiceInfo?.companyName || t("notProvided")}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">{t("taxCodeLabel")}</p>
                  <p>{taxInfo?.invoiceInfo?.taxCode || t("notProvided")}</p>
                </div>
                <div className="md:col-span-2">
                  <p className="text-sm font-medium text-gray-500">{t("addressLabel")}</p>
                  <p>{taxInfo?.invoiceInfo?.address || t("notProvided")}</p>
                </div>
              </div>
            </div>
          )}
        </section>
      </TabsContent>

      <TabsContent value="settings">
        {/* Change Password Section */}
        <section className="mb-10">
          <ChangePassword />
        </section>
        <hr className="mb-6 border-gray-200" />

        {/* Account Management Section */}
        <section className="space-y-4">
          {/* Deactivate Account Collapsible */}
          <Collapsible>
            <CollapsibleTrigger className="flex w-full items-center justify-between rounded-lg border p-4 hover:bg-muted">
              <div className="flex items-center space-x-2 text-red-600">
                <AlertTriangle className="h-5 w-5" />
                <span className="font-semibold">Yêu cầu khóa tài khoản</span>
              </div>
              <ChevronDown className="h-5 w-5" />
            </CollapsibleTrigger>
            <CollapsibleContent className="px-4 pt-2">
              <Alert variant="destructive" className="mb-4">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Lưu ý</AlertTitle>
                <AlertDescription>
                  <p className="mb-2">Quý khách sẽ không thể đăng nhập lại vào tài khoản này sau khi khóa.</p>
                  <p className="mb-2">Các tin đăng đang hiển thị của quý khách sẽ tiếp tục được hiển thị tới hết thời gian đăng tin đã chọn.</p>
                  <p className="mb-2">Số dư tiền (nếu có) trong các tài khoản của quý khách sẽ không được hoàn lại.</p>
                  <p className="mb-2">Tài khoản dịch vụ của quý khách chỉ có thể được khóa khi không còn số dư nợ.</p>
                  <p>
                    Số điện thoại chính đăng ký tài khoản này và các số điện thoại đăng tin của quý khách sẽ không thể được sử dụng lại để đăng ký tài
                    khoản mới.
                  </p>
                  <p className="mt-2">
                    Trong trường hợp bạn muốn sử dụng lại số điện thoại chính này, vui lòng liên hệ CSKH 1900.1881 để được hỗ trợ.
                  </p>
                </AlertDescription>
              </Alert>

              <Dialog open={deactivateDialogOpen} onOpenChange={setDeactivateDialogOpen}>
                <DialogTrigger asChild>
                  <Button variant="destructive">
                    <AlertTriangle className="mr-2 h-4 w-4" />
                    {t("deactivateAccountButton")}
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>{t("deactivateAccountTitle")}</DialogTitle>
                    <DialogDescription>{t("deactivateAccountDescription")}</DialogDescription>
                  </DialogHeader>

                  <div className="space-y-4 py-4">
                    <div className="space-y-2">
                      <Label htmlFor="deactivate-password">{t("passwordLabel")}</Label>
                      <Input
                        id="deactivate-password"
                        type="password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        placeholder={t("passwordPlaceholder")}
                        required
                        autoComplete="new-password"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="deactivate-reason">{t("reasonLabel")}</Label>
                      <Textarea
                        id="deactivate-reason"
                        value={reason}
                        onChange={(e) => setReason(e.target.value)}
                        placeholder={t("reasonPlaceholder")}
                        rows={3}
                      />
                    </div>
                  </div>

                  <DialogFooter>
                    <Button variant="outline" onClick={() => setDeactivateDialogOpen(false)}>
                      {t("cancelButton")}
                    </Button>
                    <Button variant="destructive" onClick={handleDeactivateAccount} disabled={isSubmitting}>
                      {isSubmitting ? <RotateCw className="mr-2 h-4 w-4 animate-spin" /> : null}
                      {t("confirmDeactivateButton")}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </CollapsibleContent>
          </Collapsible>

          {/* Delete Account Collapsible */}
          <Collapsible>
            <CollapsibleTrigger className="flex w-full items-center justify-between rounded-lg border p-4 hover:bg-muted">
              <div className="flex items-center space-x-2 text-red-600">
                <Trash2 className="h-5 w-5" />
                <span className="font-semibold">Yêu cầu xóa tài khoản</span>
              </div>
              <ChevronDown className="h-5 w-5" />
            </CollapsibleTrigger>
            <CollapsibleContent className="px-4 pt-2">
              <Alert variant="destructive" className="mb-4">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Lưu ý</AlertTitle>
                <AlertDescription>
                  <p>Gửi yêu cầu xoá toàn bộ thông tin của tài khoản. Sau khi được xử lý, toàn bộ thông tin sẽ được xoá và không thể hoàn tác.</p>
                </AlertDescription>
              </Alert>

              <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
                <DialogTrigger asChild>
                  <Button variant="destructive">
                    <Trash2 className="mr-2 h-4 w-4" />
                    {t("deleteAccountButton")}
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>{t("deleteAccountTitle")}</DialogTitle>
                    <DialogDescription>{t("deleteAccountDescription")}</DialogDescription>
                  </DialogHeader>

                  <div className="space-y-4 py-4">
                    <div className="space-y-2">
                      <Label htmlFor="delete-password">{t("passwordLabel")}</Label>
                      <Input
                        id="delete-password"
                        type="password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        placeholder={t("passwordPlaceholder")}
                        required
                        autoComplete="new-password"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="delete-reason">{t("reasonLabel")}</Label>
                      <Textarea
                        id="delete-reason"
                        value={reason}
                        onChange={(e) => setReason(e.target.value)}
                        placeholder={t("reasonPlaceholder")}
                        rows={3}
                      />
                    </div>
                  </div>

                  <DialogFooter>
                    <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
                      {t("cancelButton")}
                    </Button>
                    <Button variant="destructive" onClick={handleDeleteAccount} disabled={isSubmitting}>
                      {isSubmitting ? <RotateCw className="mr-2 h-4 w-4 animate-spin" /> : null}
                      {t("confirmDeleteButton")}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </CollapsibleContent>
          </Collapsible>
        </section>
      </TabsContent>
    </Tabs>
  );
}
