"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { checkPaymentStatus } from "@/app/actions/server/wallet";
import { formatCurrency } from "@/lib/utils";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AlertCircle, CheckCircle, XCircle, Loader2 } from "lucide-react";
import {Link} from '@/i18n/navigation';;
import { useTranslations } from 'next-intl';
import { PAYMENT_STATUS } from "@/lib/enum";

export default function PaymentResultPage() {
  const t = useTranslations('UserWalletPage');
  const [status, setStatus] = useState("loading");
  const [paymentData, setPaymentData] = useState(null);
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const refId = searchParams.get("refId");
  
  useEffect(() => {
    if (!refId) {
        setStatus(PAYMENT_STATUS.FAILED);
        return;
    }

    let attempts = 0;
    const maxAttempts = 5; // Thử lại tối đa 5 lần
    const intervalId = setInterval(async () => {
        try {
            attempts++;
            console.log(`Checking status... Attempt ${attempts}`);

            const result = await checkPaymentStatus(refId);

            if (result?.isSuccess) {
                const currentStatus = result.data.status;
                setPaymentData(result.data);

                if (currentStatus === PAYMENT_STATUS.SUCCESS) {
                    setStatus(PAYMENT_STATUS.SUCCESS);
                    clearInterval(intervalId); // Dừng lặp khi thành công
                } else if (currentStatus === PAYMENT_STATUS.FAILED || currentStatus === PAYMENT_STATUS.CANCELLED) {
                    setStatus(PAYMENT_STATUS.FAILED);
                    clearInterval(intervalId); // Dừng lặp khi thất bại
                } else {
                    // Vẫn là 'pending', tiếp tục lặp
                    setStatus(PAYMENT_STATUS.PENDING);
                }
            } else {
                // Nếu gọi API thất bại
                setStatus(PAYMENT_STATUS.FAILED);
                clearInterval(intervalId);
            }

            // Nếu đã thử quá nhiều lần mà vẫn pending, coi như thất bại
            if (attempts >= maxAttempts && status === PAYMENT_STATUS.PENDING) {
                setStatus(PAYMENT_STATUS.FAILED);
                clearInterval(intervalId);
            }

        } catch (error) {
            console.error("Payment verification error:", error);
            setStatus(PAYMENT_STATUS.FAILED);
            clearInterval(intervalId); // Dừng lặp khi có lỗi
        }
    }, 3000); // Lặp lại sau mỗi 3 giây

    // Dọn dẹp interval khi component bị unmount
    return () => clearInterval(intervalId);

}, [refId]); // Chỉ cần phụ thuộc vào orderId
  
  return (
    <div className="p-6 max-w-lg mx-auto">
      <Card className="shadow-lg">
        <CardContent className="pt-6">
          <div className="flex flex-col items-center justify-center space-y-4 p-6 text-center">
            {status === "loading" && (
              <>
                <Loader2 className="h-16 w-16 text-primary animate-spin" />
                <h2 className="text-2xl font-semibold">{t('resultLoadingTitle')}</h2>
                <p className="text-gray-500">
                  {t('resultLoadingMessage')}
                </p>
              </>
            )}
            
            {status === PAYMENT_STATUS.SUCCESS && (
              <>
                <CheckCircle className="h-16 w-16 text-green-500" />
                <h2 className="text-2xl font-semibold text-green-600">{t('resultSuccessTitle')}</h2>
                <p className="text-gray-600">
                  {t('resultSuccessMessage')}
                </p>
                
                <div className="w-full bg-gray-50 p-4 rounded-md mt-4 text-left space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-500">{t('resultTransactionIdLabel')}</span>
                    <span className="font-medium">{paymentData?.orderId}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">{t('resultAmountLabel')}</span>
                    <span className="font-medium">{formatCurrency(paymentData?.amount || 0)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">{t('resultTimeLabel')}</span>
                    <span className="font-medium">
                      {paymentData?.updatedAt 
                        ? new Date(paymentData.updatedAt).toLocaleString(router.locale || "vi-VN") 
                        : new Date().toLocaleString(router.locale || "vi-VN")}
                    </span>
                  </div>
                </div>
              </>
            )}
            
            {status === PAYMENT_STATUS.PENDING && (
              <>
                <AlertCircle className="h-16 w-16 text-amber-500" />
                <h2 className="text-2xl font-semibold text-amber-600">{t('resultPendingTitle')}</h2>
                <p className="text-gray-600">
                  {t('resultPendingMessage')}
                </p>
                
                <div className="w-full bg-gray-50 p-4 rounded-md mt-4 text-left space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-500">{t('resultTransactionIdLabel')}</span>
                    <span className="font-medium">{paymentData?.orderId}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">{t('resultAmountLabel')}</span>
                    <span className="font-medium">{formatCurrency(paymentData?.amount || 0)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">{t('resultTimeLabel')}</span>
                    <span className="font-medium">
                      {paymentData?.createdAt 
                        ? new Date(paymentData.createdAt).toLocaleString(router.locale || "vi-VN") 
                        : new Date().toLocaleString(router.locale || "vi-VN")}
                    </span>
                  </div>
                </div>
              </>
            )}
            
            {status === PAYMENT_STATUS.FAILED && (
              <>
                <XCircle className="h-16 w-16 text-red-500" />
                <h2 className="text-2xl font-semibold text-red-600">{t('resultFailedTitle')}</h2>
                <p className="text-gray-600">
                  {t('resultFailedMessage')}
                </p>
              </>
            )}
            
            <div className="flex flex-col sm:flex-row gap-3 mt-6 w-full">
              <Button className="flex-1" asChild>
                <Link href="/user/wallet">
                  {status === PAYMENT_STATUS.SUCCESS ? t('resultActionSuccess') : t('resultActionRetry')}
                </Link>
              </Button>
              
              {status !== PAYMENT_STATUS.SUCCESS && (
                <Button variant="outline" className="flex-1" asChild>
                  <Link href="/user/transactions">
                    {t('resultActionHistory')}
                  </Link>
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 