"use server";

import createMiddleware from "next-intl/middleware";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";
import { routing } from "./i18n/routing";
import { getSession, verifyJwtToken } from "./lib/sessionUtils";

// 1. Specify protected and public routes (locale-agnostic)
const protectedRoutes = [
  "/user/profile",
  "/user/payments",
  "/user/notifications",
  "/user/bds",
  "/user/wallet",
  "/user/setting",
  "/user/dashboard",
  "/user/favorite",
  "/user/transactions",
  "/test-profile-context",
];
const publicRoutes = ["/dang-ki", "/dang-nhap", "/quen-mat-khau"];

const handleI18nRouting = createMiddleware(routing);

// Regex for the dynamic EDIT route (locale-stripped)
const propertyRegex = /^\/user\/bds\/[a-zA-Z0-9-]+$/;

// Your authentication middleware logic
async function authMiddleware(request) {
  // pathname here might still have locale prefix when auth runs after intl
  const { pathname, origin } = request.nextUrl;
  // --- Robust locale detection from pathname ---
  let detectedLocale = routing.defaultLocale; // Start with default
  let localeStrippedPathname = pathname;

  for (const locale of routing.locales) {
    if (pathname === `/${locale}` || pathname.startsWith(`/${locale}/`)) {
      detectedLocale = locale;
      if (pathname === `/${locale}`) {
        localeStrippedPathname = "/"; // Root path for the locale
      } else {
        localeStrippedPathname = pathname.substring(`/${locale}`.length) || "/"; // Path after locale prefix
      }
      break; // Found the locale
    }
  }

  // 2. Check if the *locale-stripped* route is protected or public
  const isProtectedRoute = protectedRoutes.includes(localeStrippedPathname) || propertyRegex.test(localeStrippedPathname);

  const isPublicRoute = publicRoutes.includes(localeStrippedPathname);

  // Check for cookie
  const token = await getSession("Authorization");

  let isAuthenticated = false;

  if (token) {
    const decodedPayload = await verifyJwtToken(token); 

    if (decodedPayload) {
      isAuthenticated = true;
    } else {
      cookies().delete("Authorization");
    }
  }

  if (isProtectedRoute && !isAuthenticated) {
    const locale = detectedLocale;
    const loginPath = `/${locale}/dang-nhap`.replace("//", "/");
    return NextResponse.redirect(new URL(loginPath, origin));
  }

  if (isAuthenticated && isPublicRoute && !localeStrippedPathname.startsWith("/user/profile")) {
    const locale = detectedLocale;
    const profilePath = `/${locale}/user/profile`.replace("//", "/");
    return NextResponse.redirect(new URL(profilePath, origin));
  }

  return null; // Indicate auth middleware allows the request
}

export default async function middleware(request) {
  const originalPathname = request.nextUrl.pathname;

  // 1. Run next-intl middleware first.
  // It handles localization, strips locale prefix from pathname for subsequent checks,
  // and might return a response (e.g., redirect for locale preference or cookie setting).
  const intlResponse = handleI18nRouting(request);


  // 2. Run authentication middleware *after* intl middleware.
  // Pass the request object potentially modified by intl middleware.
  const authResponse = await authMiddleware(request);

  // 3. Prioritize auth response (redirect to login if required).
  if (authResponse) {
    return authResponse;
  }

  // 4. If auth allowed, check if intl middleware had a response to return.
  if (intlResponse) {
    return intlResponse;
  }

  // 5. If neither middleware returned a response, proceed to the requested page
  return NextResponse.next({
    request: {
      headers: new Headers(request.headers),
    },
  });
}

// Routes Middleware should not run on
export const config = {
  matcher: ["/((?!api|_next/static|_next/image|_vercel|.*\\.png|.*\\.svg|.*\\.webp|.*\\.jpg$).*)"],
};
