"use client";
import React from "react";
import ButtonLoading from "@/components/ui/ButtonLoading";
import { useActionState } from "react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { forgotPassword } from "@/app/actions/server/authenticate";
import { Label } from "@/components/ui/label";
import { CircleAlert, Mail } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Link } from "@/i18n/navigation";
import { useTranslations } from "next-intl";

const initialState = {
  errors: {},
  message: null,
  fieldValues: {
    email: "",
  },
};

export default function ForgetPasswordForm() {
  const t = useTranslations("ForgotPasswordPage");
  const [state, formAction, isPending] = useActionState(forgotPassword, initialState);

  return (
    <form className="mt-8 space-y-6" action={formAction}>
      {state.message && (
        <Alert variant="destructive">
          <CircleAlert className="h-4 w-4" />
          <AlertTitle className="text-red-500">{t("errorTitle")}</AlertTitle>
          <AlertDescription>
            <div>{state.message}</div>
          </AlertDescription>
        </Alert>
      )}
      <div className="space-y-2">
        <Label htmlFor="email">{t("emailLabel")}</Label>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Mail className="h-5 w-5 text-gray-400" />
          </div>
          <Input
            id="email"
            type="email"
            name="email"
            placeholder={t("emailPlaceholder")}
            defaultValue={state.fieldValues?.email}
            className="pl-10"
            required
          />
          {state.errors?.email && <p className="mt-1 text-xs text-red-500">{state.errors.email[0]}</p>}
        </div>
      </div>

      <ButtonLoading type="submit" showLoading={isPending} title={t("submitButton")} />

      <div className="text-center">
        <Link href="/dang-nhap" className="text-sm font-medium text-primary hover:underline">
          {t("backToLoginLink")}
        </Link>
      </div>
    </form>
  );
}
