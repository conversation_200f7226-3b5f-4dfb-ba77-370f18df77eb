import { useState, useCallback, useMemo, useEffect, useRef } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useAlert } from '@/contexts/AlertContext';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import {
  getPropertyByUser,
  deletePropertyById,
  verifyPropertyRemainingTimes,
  updatePropertyStatus,
  bulkDeleteProperties,
  bulkUpdatePropertyHighlight,
} from '@/app/actions/server/property';
import { PropertyStatus } from '@/lib/enum';

// Define filter keys
const FilterKeys = {
  ALL: "all",
  APPROVED: "Approved",
  PENDING_APPROVAL: "PendingApproval",
  REJECTED_BY_ADMIN: "RejectedByAdmin",
  REJECTED_DUE_TO_UNPAID: "RejectedDueToUnpaid",
  WAITING_PAYMENT: "WaitingPayment",
  EXPIRED: "Expired",
  DRAFT: "Draft",
  SOLD: "Sold",
};

export function usePropertyList(initialData, initialFilterCounts = null) {
  const [data, setData] = useState(initialData || []);
  const [loadingId, setLoadingId] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingFilterCounts, setIsLoadingFilterCounts] = useState(!initialFilterCounts); // Only load if not provided
  const [isInitialized, setIsInitialized] = useState(!!initialData && !!initialFilterCounts); // Initialize if both are provided
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [activeFilter, setActiveFilter] = useState(FilterKeys.ALL);
  const [selectedIds, setSelectedIds] = useState(new Set());
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(30);
  const { toast } = useToast();
  const { showAlert } = useAlert();
  const router = useRouter();
  const t = useTranslations("PropertyList");
  
  // Refs for performance optimization
  const searchTimeoutRef = useRef(null);
  
  // State for filter counts - use server data if available, otherwise default to 0
  const [filterCounts, setFilterCounts] = useState(() => {
    if (initialFilterCounts) {
      return {
        [FilterKeys.ALL]: initialFilterCounts.total || 0,
        [FilterKeys.APPROVED]: initialFilterCounts.approved || 0,
        [FilterKeys.PENDING_APPROVAL]: initialFilterCounts.pendingApproval || 0,
        [FilterKeys.REJECTED_BY_ADMIN]: initialFilterCounts.rejectedByAdmin || 0,
        [FilterKeys.REJECTED_DUE_TO_UNPAID]: initialFilterCounts.rejectedDueToUnpaid || 0,
        [FilterKeys.WAITING_PAYMENT]: initialFilterCounts.waitingPayment || 0,
        [FilterKeys.EXPIRED]: initialFilterCounts.expired || 0,
        [FilterKeys.DRAFT]: initialFilterCounts.draft || 0,
        [FilterKeys.SOLD]: initialFilterCounts.sold || 0,
      };
    }
    return {
      [FilterKeys.ALL]: 0,
      [FilterKeys.APPROVED]: 0,
      [FilterKeys.PENDING_APPROVAL]: 0,
      [FilterKeys.REJECTED_BY_ADMIN]: 0,
      [FilterKeys.REJECTED_DUE_TO_UNPAID]: 0,
      [FilterKeys.WAITING_PAYMENT]: 0,
      [FilterKeys.EXPIRED]: 0,
      [FilterKeys.DRAFT]: 0,
      [FilterKeys.SOLD]: 0,
    };
  });

  // Debounce search input
  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    
    searchTimeoutRef.current = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300); // 300ms debounce
    
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [searchTerm]);

  // Fetch filter counts from server
  const fetchFilterCounts = useCallback(async () => {
    setIsLoadingFilterCounts(true);
    try {
      const result = await getPropertyByUser("counts");
      if (result && result?.isSuccess && result?.data) {
        setFilterCounts({
          [FilterKeys.ALL]: result?.data?.total || 0,
          [FilterKeys.APPROVED]: result?.data?.approved || 0,
          [FilterKeys.PENDING_APPROVAL]: result?.data?.pendingApproval || 0,
          [FilterKeys.REJECTED_BY_ADMIN]: result?.data?.rejectedByAdmin || 0,
          [FilterKeys.REJECTED_DUE_TO_UNPAID]: result?.data?.rejectedDueToUnpaid || 0,
          [FilterKeys.WAITING_PAYMENT]: result?.data?.waitingPayment || 0,
          [FilterKeys.EXPIRED]: result.data.expired || 0,
          [FilterKeys.DRAFT]: result.data.draft || 0,
          [FilterKeys.SOLD]: result.data.sold || 0,
        });
      }
    } catch (error) {
      console.error("Error fetching filter counts:", error);
      toast({
        description: "Failed to fetch property statistics",
        variant: "destructive",
      });
    } finally {
      setIsLoadingFilterCounts(false);
    }
  }, [toast]);

  // Data fetching logic
  const fetchProperties = useCallback(
    async (status = null, page = 1, size = pageSize) => {
      setIsLoading(true);
      try {
        const result = await getPropertyByUser(status, page, size);
        if (result?.isSuccess && result?.data) {
          const propertyData = result?.data?.items || [];
          setData(propertyData);
          setCurrentPage(result?.data?.currentPage || page);
          fetchFilterCounts();
        } else {
          console.error("API returned error:", result?.message);
          toast({
            description: result?.message || "Failed to fetch properties",
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error("Error fetching properties:", error);
        toast({
          description: "An unexpected error occurred",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    },
    [pageSize, toast, fetchFilterCounts]
  );

  // Initialize component - only fetch what we don't have from server
  useEffect(() => {
    const initializeComponent = async () => {
      // Only fetch filter counts if not provided from server
      if (!initialFilterCounts) {
        await fetchFilterCounts();
      }

      // Handle initial data or fetch properties
      if (initialData && Array.isArray(initialData) && initialData.length > 0 && activeFilter === FilterKeys.ALL) {
        setData(initialData);
      } else {
        const statusParam = activeFilter !== FilterKeys.ALL ? activeFilter : null;
        await fetchProperties(statusParam, 1, pageSize);
      }

      setIsInitialized(true);
    };

    // If we have both initial data and filter counts, we're already initialized
    if (initialData && initialFilterCounts && activeFilter === FilterKeys.ALL) {
      setIsInitialized(true);
      return;
    }

    initializeComponent();
  }, []); // Only run once on mount

  // Handle filter changes after initialization
  useEffect(() => {
    if (!isInitialized) return;
    
    const statusParam = activeFilter !== FilterKeys.ALL ? activeFilter : null;
    fetchProperties(statusParam, 1, pageSize);
  }, [activeFilter, isInitialized, fetchProperties, pageSize]);

  // Filtering logic for search - optimized with debounced search
  const filteredData = useMemo(() => {
    if (!Array.isArray(data)) {
      return [];
    }

    if (!debouncedSearchTerm.trim()) {
      return data;
    }

    const searchLower = debouncedSearchTerm.toLowerCase();
    return data.filter((property) => {
      return property && (
        (property.name && property.name.toLowerCase().includes(searchLower)) ||
        (property.address && property.address.toLowerCase().includes(searchLower)) ||
        (property.addressSelected && property.addressSelected.toLowerCase().includes(searchLower))
      );
    });
  }, [data, debouncedSearchTerm]);

  // Memoized computed values for better performance
  const computedValues = useMemo(() => {
    const isOverallLoading = isLoading || isLoadingFilterCounts || !isInitialized;
    const isAllSelected = filteredData.length > 0 && selectedIds.size === filteredData.length;
    const isIndeterminate = selectedIds.size > 0 && selectedIds.size < filteredData.length;
    const isEmptyDatabase = !isOverallLoading && filterCounts[FilterKeys.ALL] === 0;
    const isEmptySearchResults = !isOverallLoading && filterCounts[FilterKeys.ALL] > 0 && (!filteredData || filteredData.length === 0);
    const hasResults = !isOverallLoading && filteredData && filteredData.length > 0;
    
    return {
      isOverallLoading,
      isAllSelected,
      isIndeterminate,
      isEmptyDatabase,
      isEmptySearchResults,
      hasResults,
    };
  }, [isLoading, isLoadingFilterCounts, isInitialized, filteredData, selectedIds.size, filterCounts]);

  // Handler functions
  const handleEdit = useCallback(
    (propertyId) => {
      router.push(`/user/bds/${propertyId}`);
    },
    [router]
  );

  const handleDelete = useCallback(
    async (propertyId) => {
      showAlert({
        title: t("deleteConfirmTitle"),
        message: t("deleteConfirmMessage"),
        hasCancel: true,
        onConfirm: async () => {
          setLoadingId(propertyId);
          try {
            const result = await deletePropertyById(propertyId);
            if (result?.isSuccess) {
              setSelectedIds((prev) => {
                const newSet = new Set(prev);
                newSet.delete(propertyId);
                return newSet;
              });

              toast({
                description: t("deleteSuccessToast"),
                className: "bg-teal-600 text-white",
              });

              fetchProperties(activeFilter !== FilterKeys.ALL ? activeFilter : null, 1, pageSize);
            } else {
              toast({
                description: result?.message || t("deleteErrorToast"),
                variant: "destructive",
              });
            }
          } catch (error) {
            toast({
              description: t("deleteErrorToast"),
              variant: "destructive",
            });
          } finally {
            setLoadingId(null);
          }
        },
      });
    },
    [showAlert, toast, t, fetchProperties, activeFilter, pageSize]
  );

  const handleSendToReviewRequest = useCallback(
    async (propertyId) => {
      setLoadingId(propertyId);
      try {
        const remainingTimes = await verifyPropertyRemainingTimes(propertyId);
        if (remainingTimes?.isSuccess) {
          // Check if remaining times is 0
          if (remainingTimes?.data === 0) {
            setLoadingId(null);
            // Return the remaining times data so PropertyCard can handle the UI
            return { isSuccess: true, remainingTimes: 0 };
          }
          
          showAlert({
            title: t("verifyConfirmTitle"),
            message: t("verifyConfirmMessage", { remainingTimes: remainingTimes?.data }),
            hasCancel: true,
            onConfirm: async () => {
              try {
                setLoadingId(propertyId);
                const formData = new FormData();
                formData.append("propertyId", propertyId);
                formData.append("status", PropertyStatus.PENDING_APPROVAL);

                const result = await updatePropertyStatus(formData);
                if (result?.isSuccess) {
                  toast({
                    description: t("verifySuccessToast"),
                    className: "bg-teal-600 text-white",
                  });

                  fetchProperties(activeFilter !== FilterKeys.ALL ? activeFilter : null, 1, pageSize);
                } else {
                  toast({
                    description: result?.message || t("verifyErrorToast"),
                    variant: "destructive",
                  });
                }
              } catch (error) {
                console.error("Error sending verification request:", error);
                toast({
                  description: t("verifyGenericErrorToast"),
                  variant: "destructive",
                });
              } finally {
                setLoadingId(null);
              }
            },
            hasCancel: true,
            onCancel: () => setLoadingId(null),
          });
          
          return { isSuccess: true, remainingTimes: remainingTimes?.data };
        } else {
          setLoadingId(null);
          toast({
            description: remainingTimes?.message || t("verifyCheckErrorToast"),
            className: "bg-red-600 text-white",
          });
          return { isSuccess: false, message: remainingTimes?.message };
        }
      } catch (error) {
        setLoadingId(null);
        console.error("Error checking remaining verification times:", error);
        toast({
          description: t("verifyGenericErrorToast"),
          className: "bg-red-600 text-white",
        });
        return { isSuccess: false, error };
      }
    },
    [showAlert, toast, t, fetchProperties, activeFilter, pageSize]
  );

  const handleBulkHighlight = useCallback(() => {
    if (selectedIds.size === 0) return;

    showAlert({
      title: t("bulkHighlightConfirmTitle", { count: selectedIds.size }),
      message: t("bulkHighlightConfirmMessage"),
      hasCancel: true,
      onConfirm: async () => {
        setLoadingId("bulk-highlight");
        try {
          const result = await bulkUpdatePropertyHighlight(Array.from(selectedIds), true);

          // Handle the new API response structure
          if (result?.isSuccess) {
            const successCount = result?.data?.isSuccessfullyHighlighted || 0;
            const alreadyHighlighted = result?.data?.alreadyHighlighted || 0;
            const failed = result?.data?.failed || 0;
            const totalCost = result?.data?.totalCost || 0;

            // Show detailed success message
            let successMessage = "";
            if (successCount > 0) {
              successMessage += t("bulkHighlightSuccessToast", { count: successCount });
              if (totalCost > 0) {
                successMessage += ` (${totalCost.toLocaleString('vi-VN')} VND)`;
              }
            }
            if (alreadyHighlighted > 0) {
              if (successMessage) successMessage += ". ";
              successMessage += t("bulkHighlightAlreadyHighlighted", { count: alreadyHighlighted });
            }
            if (failed > 0) {
              if (successMessage) successMessage += ". ";
              successMessage += t("bulkHighlightFailed", { count: failed });
            }

            toast({
              description: successMessage,
              className: "bg-teal-600 text-white",
            });

            // Clear selection and refresh properties
            setSelectedIds(new Set());
            fetchProperties(activeFilter !== FilterKeys.ALL ? activeFilter : null, 1, pageSize);
          } else {
            // Handle wallet insufficient balance and other errors
            const errorMessage = result?.message || t("bulkHighlightErrorToast");

            // Check if it's a wallet balance error
            if (errorMessage.includes("Số dư ví không đủ") || errorMessage.includes("wallet balance")) {
              toast({
                description: errorMessage,
                variant: "destructive",
                duration: 8000, // Show longer for wallet errors
              });
            } else {
              toast({
                description: errorMessage,
                variant: "destructive",
              });
            }
          }
        } catch (error) {
          console.error("Error highlighting properties:", error);

          // Handle network errors or API errors
          let errorMessage = t("bulkHighlightGenericErrorToast");
          if (error.response?.data?.message) {
            errorMessage = error.response.data.message;
          } else if (error.message) {
            errorMessage = error.message;
          }

          toast({
            description: errorMessage,
            variant: "destructive",
          });
        } finally {
          setLoadingId(null);
        }
      },
    });
  }, [selectedIds, showAlert, toast, fetchProperties, activeFilter, pageSize, t, setSelectedIds]);

  const handleBulkRenew = useCallback(() => {
    if (selectedIds.size === 0) return;

    // For now, just show a message since renew functionality is not implemented in the API
    toast({
      description: t("bulkRenewFeatureInDevelopment"),
      variant: "default",
    });

    // When API is available, implement similar to handleBulkHighlight
    // using the appropriate API endpoint
  }, [selectedIds, toast, t]);

  const handleBulkDelete = useCallback(() => {
    if (selectedIds.size === 0) return;

    showAlert({
      title: t("bulkDeleteConfirmTitle", { count: selectedIds.size }),
      message: t("bulkDeleteConfirmMessage"),
      hasCancel: true,
      onConfirm: async () => {
        setLoadingId("bulk-delete");
        try {
          const result = await bulkDeleteProperties(Array.from(selectedIds));

          if (result?.isSuccess) {
            setSelectedIds(new Set());

            toast({
              description: t("bulkDeleteSuccessToast", { count: selectedIds.size }),
              className: "bg-teal-600 text-white",
            });

            fetchProperties(activeFilter !== FilterKeys.ALL ? activeFilter : null, 1, pageSize);
          } else {
            toast({
              description: result?.message || t("bulkDeleteErrorToast"),
              variant: "destructive",
            });
          }
        } catch (error) {
          console.error("Error deleting properties:", error);
          toast({
            description: t("bulkDeleteGenericErrorToast"),
            variant: "destructive",
          });
        } finally {
          setLoadingId(null);
        }
      },
    });
  }, [selectedIds, showAlert, toast, fetchProperties, activeFilter, pageSize, t]);

  return {
    // State
    data,
    filteredData,
    loadingId,
    setLoadingId,
    searchTerm,
    setSearchTerm,
    activeFilter,
    setActiveFilter,
    selectedIds,
    setSelectedIds,
    currentPage,
    pageSize,
    filterCounts,

    // Computed values
    ...computedValues,

    // Functions
    fetchProperties,
    fetchFilterCounts,
    handleEdit,
    handleDelete,
    handleSendToReviewRequest,
    handleBulkHighlight,
    handleBulkRenew,
    handleBulkDelete,

    // Constants
    FilterKeys,
  };
}
