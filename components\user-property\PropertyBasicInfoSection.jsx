"use client";

import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { useTranslations } from "next-intl";
import { Separator } from "@/components/ui/separator";
import { Card } from "../ui/card";
import { CollapsibleSection } from "../ui/collapsible-section";
import { PostType, PropertyType } from "@/lib/enum";

const PropertyBasicInfoSection = ({ form, isFormDisabled }) => {
  const t = useTranslations("PropertyForm");

  return (
    <Card className="border hover:border-blue-200 transition-all duration-300 mb-3">
      <CollapsibleSection title={t("basicInfo")} subTitle={t("requiredInfo")} >
        <Separator className="mb-6" />
        <FormField
          control={form.control}
          name="postType"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("postType")}</FormLabel>
              <FormControl>
                <RadioGroup onValueChange={field.onChange} defaultValue={field.value} className="flex gap-4 mt-2">
                  <FormItem className="flex items-center space-x-3 space-y-0">
                    <FormControl>
                      <RadioGroupItem value={PostType.SALE} disabled={isFormDisabled} />
                    </FormControl>
                    <FormLabel className="font-normal">{t("sale")}</FormLabel>
                  </FormItem>
                  <FormItem className="flex items-center space-x-3 space-y-0">
                    <FormControl>
                      <RadioGroupItem value={PostType.RENT} disabled={isFormDisabled} />
                    </FormControl>
                    <FormLabel className="font-normal">{t("rent")}</FormLabel>
                  </FormItem> 
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <FormField
              control={form.control}
              name="propertyType"
              render={({ field }) => (
                <FormItem className="mt-3">
                  <FormLabel>{t("propertyType")}</FormLabel>
                  <FormControl>
                    <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isFormDisabled}>
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder={t("selectPropertyType")} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem key={PropertyType.HOUSE} value={PropertyType.HOUSE}>{t("house")}</SelectItem>
                        <SelectItem key={PropertyType.APARTMENT} value={PropertyType.APARTMENT}>{t("apartment")}</SelectItem>
                        <SelectItem key={PropertyType.MOTEL} value={PropertyType.MOTEL}>{t("motel")}</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <div>
            <FormField
              control={form.control}
              name="price"
              render={({ field }) => (
                <FormItem className="mt-3">
                  <FormLabel>{t("price")}</FormLabel>
                  <FormControl>
                    <Input placeholder={t("pricePlaceholder")} {...field} type="number" suffix=" VND" disabled={isFormDisabled} />
                  </FormControl>
                  <FormMessage></FormMessage>
                </FormItem>
              )}
            />
          </div>
        </div>
      </CollapsibleSection>
    </Card>
  );
};

export default PropertyBasicInfoSection;
