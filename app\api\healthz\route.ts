import { NextRequest, NextResponse } from "next/server";

export async function GET(req: NextRequest) {
  // <PERSON><PERSON><PERSON> các biến env cần kiểm tra
  const envVars = {
    NODE_ENV: process.env.NODE_ENV,
    API_URL: process.env.API_URL,
    // Lưu ý: Không expose secret production ra client
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
  };

  console.log("Server-side env:");
  console.log("API_URL:", process.env.API_URL);

  return NextResponse.json({
    status: "ok",
    env: envVars,
  });
}
