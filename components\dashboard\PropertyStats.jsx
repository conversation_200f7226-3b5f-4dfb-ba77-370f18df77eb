import { Home, Star, Eye, Clock, Heart } from "lucide-react";

export default function PropertyStats({ propertyStats }) {
  const stats = [
    {
      label: "Tổng số BĐS",
      value: propertyStats.totalProperties,
      icon: Home,
      color: "text-navy-blue"
    },
    {
      label: "Đang hoạt động",
      value: propertyStats.activeProperties,
      icon: Clock,
      color: "text-green-600"
    },
    {
      label: "Đã hết hạn",
      value: propertyStats.expiredProperties,
      icon: Clock,
      color: "text-coral-500"
    },
    {
      label: "Lượt xem",
      value: propertyStats.totalViews,
      icon: Eye,
      color: "text-blue-500"
    },
    {
      label: "Đã lưu",
      value: propertyStats.favoriteProperties,
      icon: Heart,
      color: "text-coral-600"
    }
  ];

  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-5 gap-4">
      {stats.map((stat, index) => (
        <div key={index} className="flex flex-col items-center justify-center p-3 bg-gray-50 rounded-md">
          <div className={`p-2 rounded-full bg-opacity-20 ${stat.color.replace('text', 'bg')} mb-2`}>
            <stat.icon className={`h-5 w-5 ${stat.color}`} />
          </div>
          <span className="text-2xl font-bold">{stat.value}</span>
          <span className="text-xs text-gray-500 text-center">{stat.label}</span>
        </div>
      ))}
    </div>
  );
} 