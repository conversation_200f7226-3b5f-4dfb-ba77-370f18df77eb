"use client";

import { useEffect, useRef, useState } from "react";
import { Loader2 } from "lucide-react";

export default function PropertyDetailMap({ property }) {
  const mapContainerRef = useRef(null);
  const [map, setMap] = useState(null);
  const [loading, setLoading] = useState(true);
  const mapInstanceRef = useRef(null);

  // Extract coordinates from property
  const latitude = parseFloat(property?.latitude) || 0;
  const longitude = parseFloat(property?.longitude) || 0;
  const address = property?.address || "";

  // Initialize map on component mount
  useEffect(() => {
    if (!mapContainerRef.current || !latitude || !longitude) return;

    // Load Goong Maps script
    const script = document.createElement("script");
    script.src = "https://cdn.jsdelivr.net/npm/@goongmaps/goong-js@1.0.9/dist/goong-js.js";
    script.async = true;
    document.body.appendChild(script);

    // Load CSS
    const link = document.createElement("link");
    link.href = "https://cdn.jsdelivr.net/npm/@goongmaps/goong-js@1.0.9/dist/goong-js.css";
    link.rel = "stylesheet";
    document.head.appendChild(link);

    let mapInstance = null;

    script.onload = () => {
      if (!window.goongjs) {
        console.error("Goong Maps failed to load");
        setLoading(false);
        return;
      }

      window.goongjs.accessToken = process.env.NEXT_PUBLIC_GOONG_MAPTILES_KEY || '8qzxZAuxcsctSlmOszInchP1A5GrmRBHJwCBCjO6'; // Use your Goong Maps API key

      mapInstance = new window.goongjs.Map({
        container: mapContainerRef.current,
        style: 'https://tiles.goong.io/assets/goong_map_web.json',
        center: [longitude, latitude],
        zoom: 15
      });

      mapInstanceRef.current = mapInstance;

      mapInstance.on('load', () => {
        setMap(mapInstance);
        setLoading(false);
        
        // Add property marker
        addPropertyMarker(mapInstance);
      });
    };

    return () => {
      // Clean up
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
      if (document.body.contains(script)) {
        document.body.removeChild(script);
      }
      if (document.head.contains(link)) {
        document.head.removeChild(link);
      }
    };
  }, [latitude, longitude]);

  // Function to add property marker
  const addPropertyMarker = (mapInstance) => {
    if (!mapInstance) return;
    
    // Create marker element
    const el = document.createElement('div');
    el.className = 'property-marker';
    el.style.width = '32px';
    el.style.height = '32px';
    el.style.backgroundImage = 'url(/marker.svg)';
    el.style.backgroundSize = 'cover';
    
    // Create popup with property information
    const popupContent = `
      <div style="max-width: 200px;">
        <h3 style="font-weight: bold; margin-bottom: 5px;">${property.name || 'Property'}</h3>
        <p style="font-weight: bold; color: #10b981;">${property.price?.toLocaleString('vi-VN') || ''} VNĐ</p>
        <p style="font-size: 12px; color: #666;">${address}</p>
        <div style="display: flex; justify-content: space-between; font-size: 12px; color: #666; margin-top: 5px;">
          <span>${property.area || 0} m²</span>
          <span>${property.rooms || 0} PN</span>
          <span>${property.toilets || 0} PT</span>
        </div>
      </div>
    `;
    
    // Create popup
    const popup = new window.goongjs.Popup({ 
      offset: 25,
      closeButton: true,
      closeOnClick: false
    }).setHTML(popupContent);
    
    // Create marker
    const marker = new window.goongjs.Marker(el)
      .setLngLat([longitude, latitude])
      .setPopup(popup)
      .addTo(mapInstance);
    
    // Open popup by default
    popup.addTo(mapInstance);
  };

  // Add map controls
  const handleZoomIn = () => {
    if (map) map.zoomIn();
  };

  const handleZoomOut = () => {
    if (map) map.zoomOut();
  };

  // Open directions in a new tab
  const openDirections = () => {
    if (latitude && longitude) {
      const url = `https://maps.google.com/maps?daddr=${latitude},${longitude}`;
      window.open(url, '_blank');
    }
  };

  return (
    <div className="relative h-64 rounded-md overflow-hidden">
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 z-10">
          <div className="flex flex-col items-center">
            <Loader2 className="h-8 w-8 text-teal-500 animate-spin mb-2" />
            <p className="text-gray-600">Đang tải bản đồ...</p>
          </div>
        </div>
      )}
      <div 
        ref={mapContainerRef} 
        className="h-full w-full"
      />
      
      {/* Map Controls */}
      <div className="absolute bottom-4 right-4 flex flex-col gap-2">
        <button 
          className="bg-white p-2 rounded-full shadow-md hover:bg-gray-100 transition-colors"
          onClick={handleZoomIn}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <line x1="12" y1="5" x2="12" y2="19"></line>
            <line x1="5" y1="12" x2="19" y2="12"></line>
          </svg>
        </button>
        <button 
          className="bg-white p-2 rounded-full shadow-md hover:bg-gray-100 transition-colors"
          onClick={handleZoomOut}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <line x1="5" y1="12" x2="19" y2="12"></line>
          </svg>
        </button>
      </div>
      
      {/* Directions button */}
      <button 
        onClick={openDirections}
        className="absolute top-2 left-2 bg-white px-2 py-1 rounded-md text-xs font-medium shadow-xs hover:bg-gray-100 transition-colors flex items-center gap-1"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <polygon points="3 11 22 2 13 21 11 13 3 11"></polygon>
        </svg>
        Xem đường
      </button>
      
      {/* Add custom CSS for markers */}
      <style jsx global>{`
        .property-marker {
          background-size: cover;
          transition: all 0.3s ease;
          cursor: pointer;
        }
        
        .mapboxgl-popup {
          z-index: 10;
        }
        
        .mapboxgl-popup-content {
          padding: 15px;
          border-radius: 8px;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
      `}</style>
    </div>
  );
}
