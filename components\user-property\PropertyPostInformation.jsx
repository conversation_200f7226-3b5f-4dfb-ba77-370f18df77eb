import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { useTranslations } from "next-intl";
import { Card } from "../ui/card";
import { CollapsibleSection } from "../ui/collapsible-section";

export default function PropertyPostInformation({ form, isFormDisabled }) {
  const t = useTranslations("PropertyPostInformation");

  return (
    <Card className="border hover:border-blue-200 transition-all duration-300 mb-3">
      <CollapsibleSection title={t("postInformation")} subTitle={t("requiredInformation")}>
        <Separator className="mb-6" />
        <FormField
          control={form.control}
          name="name"
          className="mt-3"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("title")}</FormLabel>
              <FormControl>
                <Input placeholder={t("titlePlaceholder")} {...field} disabled={isFormDisabled} readOnly={isFormDisabled} />
              </FormControl>
              <FormDescription className="text-xs">{t("titleDescription")}</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="description"
          className="mt-3"
          render={({ field }) => (
            <FormItem className="mt-3">
              <FormLabel>{t("introduction")}</FormLabel>
              <FormControl>
                <Textarea placeholder={t("descriptionPlaceholder")} {...field} rows={10} disabled={isFormDisabled} readOnly={isFormDisabled} />
              </FormControl>
              <FormDescription className="text-xs">{t("descriptionDescription")}</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      </CollapsibleSection>
    </Card>
  );
}
