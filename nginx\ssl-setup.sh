#!/bin/sh

# SSL Setup Script for YEZHome Frontend
# This script handles SSL certificate generation and Nginx configuration

set -e

# Environment variables (should be set when running the container)
DOMAIN=${DOMAIN:-"localhost"}
EMAIL=${CERTBOT_EMAIL:-"<EMAIL>"}
STAGING=${CERTBOT_STAGING:-"false"}

echo "Starting SSL setup for domain: $DOMAIN"

# Function to check if SSL certificate exists
check_ssl_cert() {
    if [ -f "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" ] && [ -f "/etc/letsencrypt/live/$DOMAIN/privkey.pem" ]; then
        return 0
    else
        return 1
    fi
}

# Function to generate SSL certificate
generate_ssl_cert() {
    echo "Generating SSL certificate for $DOMAIN"
    
    # Prepare certbot command
    CERTBOT_ARGS="certonly --webroot --webroot-path=/var/www/certbot --email $EMAIL --agree-tos --no-eff-email -d $DOMAIN"
    
    # Add staging flag if specified
    if [ "$STAGING" = "true" ]; then
        CERTBOT_ARGS="$CERTBOT_ARGS --staging"
        echo "Using Let's Encrypt staging environment"
    fi
    
    # Run certbot
    certbot $CERTBOT_ARGS
    
    if [ $? -eq 0 ]; then
        echo "SSL certificate generated successfully"
        return 0
    else
        echo "Failed to generate SSL certificate"
        return 1
    fi
}

# Function to configure Nginx with SSL
configure_nginx_ssl() {
    echo "Configuring Nginx with SSL for $DOMAIN"
    
    # Create SSL-enabled configuration
    cat > /etc/nginx/conf.d/ssl.conf << EOF
# HTTPS server configuration
server {
    listen 443 ssl http2;
    server_name $DOMAIN;
    
    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/$DOMAIN/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$DOMAIN/privkey.pem;
    
    # SSL Security
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    
    # Rate limiting
    limit_req zone=general burst=50 nodelay;
    
    # Root directory
    root /usr/share/nginx/html;
    index index.html index.htm;
    
    # Main location block
    location / {
        try_files \$uri \$uri/ /index.html;
        
        # Cache control for static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Vary Accept-Encoding;
        }
        
        # Cache control for HTML files
        location ~* \.html$ {
            expires 1h;
            add_header Cache-Control "public, must-revalidate";
        }
    }
}
EOF

    # Update HTTP server to redirect to HTTPS
    cat > /etc/nginx/conf.d/http-redirect.conf << EOF
# HTTP server - redirects to HTTPS
server {
    listen 80;
    server_name $DOMAIN;
    
    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # Let's Encrypt challenge
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
        try_files \$uri \$uri/ =404;
    }
    
    # Redirect all other traffic to HTTPS
    location / {
        return 301 https://\$server_name\$request_uri;
    }
}
EOF

    echo "Nginx SSL configuration created"
}

# Function to setup certificate renewal
setup_renewal() {
    echo "Setting up certificate renewal"
    
    # Create renewal script
    cat > /usr/local/bin/renew-cert.sh << 'EOF'
#!/bin/sh
certbot renew --quiet --no-self-upgrade
if [ $? -eq 0 ]; then
    nginx -s reload
fi
EOF
    chmod +x /usr/local/bin/renew-cert.sh
    
    # Add to crontab (runs twice daily)
    echo "0 */12 * * * /usr/local/bin/renew-cert.sh" | crontab -
    
    echo "Certificate renewal configured"
}

# Main execution
main() {
    # Skip SSL setup for localhost or if disabled
    if [ "$DOMAIN" = "localhost" ] || [ "$DOMAIN" = "" ]; then
        echo "Skipping SSL setup for localhost"
        return 0
    fi
    
    # Wait for Nginx to start
    sleep 5
    
    # Check if certificate already exists
    if check_ssl_cert; then
        echo "SSL certificate already exists for $DOMAIN"
        configure_nginx_ssl
        nginx -s reload
    else
        echo "SSL certificate not found, generating new one"
        
        # Try to generate certificate (retry up to 3 times)
        for i in 1 2 3; do
            if generate_ssl_cert; then
                configure_nginx_ssl
                setup_renewal
                nginx -s reload
                echo "SSL setup completed successfully"
                break
            else
                echo "Attempt $i failed, retrying in 30 seconds..."
                sleep 30
            fi
        done
    fi
}

# Run main function
main
