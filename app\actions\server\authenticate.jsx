"use server";

import { handleErrorResponse } from "@/lib/apiUtils";
import { changePasswordSchema, forgetPasswordSchema, loginSchema, registerSchema, resetPasswordSchema } from "@/lib/schemas/authSchema";
import { createSession, deleteSession, fetchWithAuth, getJwtInfo, getSession, fetchWithoutAuth, verifyJwtToken } from "@/lib/sessionUtils";
import { redirect } from "next/navigation";
import { revalidatePath } from "next/cache";
import { normalizeVNPhoneNumber } from "@/lib/utils";

export async function registerUser(prevState, formData) {
  const formDataObject = Object.fromEntries(formData.entries());

  const validatedFields = registerSchema.safeParse(formDataObject);

  if (!validatedFields.success) {
    return {
      errors: validatedFields.error.flatten().fieldErrors,
      message: "<PERSON><PERSON> liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.",
      fieldValues: formDataObject,
    };
  }

  // validate phone number
  const normalizedPhone = normalizeVNPhoneNumber(validatedFields.data.phone)
  if (!normalizedPhone) {
    return {
      errors: { phone: ["Số điện thoại không hợp lệ"] },
      message: "Số điện thoại không hợp lệ",
      fieldValues: formDataObject,
    };
  }

  const result = await fetchWithoutAuth(`${process.env.API_URL}/api/Auth/register`, {
    method: "POST",
    body: JSON.stringify(validatedFields.data),
  });

  if (!result.isSuccess) {
    return {
      isSuccess: false,
      message: result.message || "Registration failed. Please try again.",
      fieldValues: formDataObject,
    };
  }
  
  redirect("/dang-ki/dang-ki-thanh-cong");

}

export async function loginUser(prevState, formData) {
  const validatedFields = loginSchema.safeParse({
    email: formData.get("email"),
    password: formData.get("password"),
  });

  if (!validatedFields.success) {
    return {
      errors: validatedFields.error.flatten().fieldErrors,
      message: "Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.",
      fieldValues: {
        email: formData.get("email"),
      },
    };
  }

  let urlCallback = "/user/profile";

  const result = await fetchWithoutAuth(`${process.env.API_URL}/api/Auth/login`, {
    method: "POST",
    body: JSON.stringify({
      email: validatedFields.data.email,
      password: validatedFields.data.password,
    }),
  });

  if (result.isSuccess) {
    const responseData = result.data;
    const token = responseData.token;
    const user = {
      id: responseData.id,
      fullName: responseData.fullName,
      email: responseData.email,
      userType: responseData.userType,
      phone: responseData.phone,
      lastLogin: responseData.lastLogin,
    };

    await createSession("Authorization", token);
    await createSession("UserProfile", JSON.stringify(user));
  } else {
    return {
      errors: null,
      message: result.message,
      fieldValues: {
        email: formData.get("email"),
      },
    };
  }

  await new Promise((resolve) => setTimeout(resolve, 100));
  revalidatePath("/");
  redirect(urlCallback);
}

export async function changePassword(prevState, formData) {
  const formDataObject = Object.fromEntries(formData.entries());

  const validatedFields = changePasswordSchema.safeParse(formDataObject);

  if (!validatedFields.success) {
    return {
      errors: validatedFields.error.flatten().fieldErrors,
      message: "Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.",
      fieldValues: formDataObject,
    };
  }

  const jwtData = await getJwtInfo();

  let payload = {
    email: jwtData.email,
    oldPassword: validatedFields.data.oldPassword,
    newPassword: validatedFields.data.newPassword,
  };

  return await fetchWithAuth(`${process.env.API_URL}/api/Auth/me/password`, {
    method: "PATCH",
    body: JSON.stringify(payload),
    headers: {
      "Content-Type": "application/json",
    },
  });
}

export async function forgotPassword(prevState, formData) {
  const formDataObject = Object.fromEntries(formData.entries());

  const validatedFields = forgetPasswordSchema.safeParse(formDataObject);

  if (!validatedFields.success) {
    return {
      errors: validatedFields.error.flatten().fieldErrors,
      message: "Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.",
      fieldValues: formDataObject,
    };
  }

  const result = await fetchWithoutAuth(`${process.env.API_URL}/api/Auth/forgot-password`, {
    method: "POST",
    body: JSON.stringify({
      email: validatedFields.data.email,
    }),
    headers: {
      "Content-Type": "application/json",
    },
  });

  if (!result.isSuccess) {
    return {
      isSuccess: false,
      message: result.data?.message || result.title,
    };
  }
  else {
    redirect("/da-gui-email-khoi-phuc-mat-khau");
  }
}

export async function getUserProfile() {
  return await fetchWithAuth(`${process.env.API_URL}/api/Auth/me`);
}

export async function validateTokenDirectlyFromAPIServer() {
  return await fetchWithAuth(`${process.env.API_URL}/api/Auth/validate-token`);
}

export async function validateTokenServer() {
  const token = await getSession("Authorization");
  if (!token) {
    return { isLoggedIn: false, isExpired: false, errorType: "no_token" };
  }

  const decoded = await verifyJwtToken(token);
  if (!decoded) {
    deleteSession("Authorization");
    deleteSession("UserProfile");
    return { isLoggedIn: false, isExpired: true };
  }

  return { isLoggedIn: true, isExpired: false };
}

export async function resetPassword(prevState, formData) {
  const formDataObject = Object.fromEntries(formData.entries());

  const validatedFields = resetPasswordSchema.safeParse(formDataObject);

  if (!validatedFields.success) {
    return {
      errors: validatedFields.error.flatten().fieldErrors,
      message: "Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.",
      fieldValues: formDataObject,
    };
  }

  const result = await fetchWithoutAuth(`${process.env.API_URL}/api/Auth/reset-password`, {
    method: "POST",
    body: JSON.stringify({
      userId: validatedFields.data.userId,
      token: validatedFields.data.token,
      newPassword: validatedFields.data.newPassword,
      confirmPassword: validatedFields.data.confirmPassword,
    }),
    headers: {
      "Content-Type": "application/json",
    },
  });

  if (!result.isSuccess) {
    return {
      isSuccess: false,
      message: result.message || result.title || "Đặt lại mật khẩu thất bại. Vui lòng thử lại.",
      fieldValues: formDataObject,
    };
  }
  
  revalidatePath("/");
  redirect("/dang-nhap");
}

export async function logout() {
  await deleteSession("Authorization");
  await deleteSession("UserProfile");
  redirect("/");
}
