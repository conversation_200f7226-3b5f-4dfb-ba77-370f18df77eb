name: 🩺 Frontend Health Check

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to check'
        required: true
        type: choice
        options:
          - staging
          - production
        default: staging
      check_type:
        description: 'Type of health check'
        required: true
        type: choice
        options:
          - basic
          - detailed
        default: basic

jobs:
  health-check:
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment }}

    steps:
      - name: Set environment variables
        run: |
          if [[ "${{ github.event.inputs.environment }}" == "staging" ]]; then
            echo "FRONTEND_URL=${{ vars.STAGING_FRONTEND_URL }}" >> $GITHUB_ENV
            echo "FALLBACK_URL=http://${{ secrets.DROPLET_IP }}:${{ vars.STAGING_FRONTEND_PORT || 3001 }}" >> $GITHUB_ENV
            echo "CONTAINER_NAME=yezhome-fe-staging" >> $GITHUB_ENV
          else
            echo "FRONTEND_URL=${{ vars.PRODUCTION_FRONTEND_URL }}" >> $GITHUB_ENV
            echo "FALLBACK_URL=http://${{ secrets.DROPLET_IP }}:${{ vars.PRODUCTION_FRONTEND_PORT || 3000 }}" >> $GITHUB_ENV
            echo "CONTAINER_NAME=yezhome-fe-production" >> $GITHUB_ENV
          fi

      - name: Basic Health Check
        run: |
          echo "🏥 Performing basic health check for ${{ github.event.inputs.environment }}..."
          echo "🌐 Checking URL: ${{ env.FRONTEND_URL }}"
          
          # Try primary URL first
          if curl -f --max-time 30 --silent --head "${{ env.FRONTEND_URL }}/"; then
            echo "✅ Primary URL is responding"
            STATUS="healthy"
          elif curl -f --max-time 30 --silent --head "${{ env.FALLBACK_URL }}/"; then
            echo "⚠️  Primary URL failed, but fallback URL is responding"
            STATUS="warning"
          else
            echo "❌ Both primary and fallback URLs are not responding"
            STATUS="unhealthy"
          fi
          
          echo "HEALTH_STATUS=${STATUS}" >> $GITHUB_ENV

      - name: Detailed Health Check
        if: github.event.inputs.check_type == 'detailed'
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.DROPLET_IP }}
          username: ${{ secrets.DROPLET_USERNAME }}
          key: ${{ secrets.DROPLET_SSH_KEY }}
          script: |
            echo "🔍 Performing detailed health check..."
            
            # Check if container is running
            if docker ps | grep -q ${{ env.CONTAINER_NAME }}; then
              echo "✅ Container ${{ env.CONTAINER_NAME }} is running"
              
              # Get container details
              echo "📊 Container Status:"
              docker ps --filter "name=${{ env.CONTAINER_NAME }}" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
              
              # Check container logs (last 20 lines)
              echo ""
              echo "📝 Recent Container Logs:"
              docker logs --tail 20 ${{ env.CONTAINER_NAME }}
              
              # Check container resource usage
              echo ""
              echo "💾 Container Resource Usage:"
              docker stats ${{ env.CONTAINER_NAME }} --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"
              
            else
              echo "❌ Container ${{ env.CONTAINER_NAME }} is not running"
              
              # Check if container exists but is stopped
              if docker ps -a | grep -q ${{ env.CONTAINER_NAME }}; then
                echo "⚠️  Container exists but is stopped"
                echo "📝 Container logs:"
                docker logs --tail 50 ${{ env.CONTAINER_NAME }}
              else
                echo "❌ Container does not exist"
              fi
            fi
            
            # Check available disk space
            echo ""
            echo "💽 Disk Usage:"
            df -h /

      - name: Health Check Summary
        if: always()
        run: |
          echo "🏥 Health Check Summary for ${{ github.event.inputs.environment }}"
          echo "=================================="
          echo "Environment: ${{ github.event.inputs.environment }}"
          echo "Check Type: ${{ github.event.inputs.check_type }}"
          echo "Primary URL: ${{ env.FRONTEND_URL }}"
          echo "Fallback URL: ${{ env.FALLBACK_URL }}"
          echo "Status: ${{ env.HEALTH_STATUS || 'unknown' }}"
          echo "Timestamp: $(date -u '+%Y-%m-%d %H:%M:%S UTC')"
          echo ""
          
          case "${{ env.HEALTH_STATUS }}" in
            "healthy")
              echo "🎉 All systems are operational!"
              ;;
            "warning")
              echo "⚠️  Service is running but may have issues. Check logs for details."
              ;;
            "unhealthy")
              echo "🚨 Service appears to be down. Immediate attention required!"
              exit 1
              ;;
            *)
              echo "❓ Health status could not be determined."
              ;;
          esac
