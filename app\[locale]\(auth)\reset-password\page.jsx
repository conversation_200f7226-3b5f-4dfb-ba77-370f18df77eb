import ResetPasswordForm from "./ResetPasswordForm";
import { getTranslations } from 'next-intl/server';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { CircleAlert } from "lucide-react";
import { Link } from '@/i18n/navigation';

const Page = async ({ searchParams }) => {
  const t = await getTranslations('ResetPasswordPage');
  
  // Get userId and token from URL parameters
  const params = await searchParams;
  const userId = params.userId;
  const token = params.token;

  // Validate that both parameters are present
  if (!userId || !token) {
    return (
      <div className="min-h-screen flex pt-8 justify-center bg-background px-4 py-12 sm:px-6 lg:px-8">
        <div className="w-full max-w-md space-y-8">
          <div className="text-center">
            <h2 className="text-2xl font-bold">{t('title')}</h2>
            <p className="mt-2 text-sm text-muted-foreground">
              {t('description')}
            </p>
          </div>

          <Alert variant="destructive">
            <CircleAlert className="h-4 w-4" />
            <AlertTitle className="text-red-500">{t('errorTitle')}</AlertTitle>
            <AlertDescription>{t('missingParameters')}</AlertDescription>
          </Alert>

          <div className="text-center">
            <Link href="/dang-nhap" className="text-sm font-medium text-primary hover:underline">
              {t('backToLoginLink')}
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex pt-8 justify-center bg-background px-4 py-12 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-8">
        <div className="text-center">
          <h2 className="text-2xl font-bold">{t('title')}</h2>
          <p className="mt-2 text-sm text-muted-foreground">
            {t('description')}
          </p>
        </div>

        <ResetPasswordForm userId={userId} token={token} />
      </div>
    </div>
  );
};

export default Page;
