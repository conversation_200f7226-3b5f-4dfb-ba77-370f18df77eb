"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { CheckCircle, FileText, Info, Calendar, CreditCard, Clock } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { useTranslations } from "next-intl";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/contexts/AuthContext";
import { DEFAULT_POST_PRICE } from "@/lib/enum";
import { format } from "date-fns";
import { vi } from "date-fns/locale";
import Link from "next/link";

const SuccessPageClient = ({ property }) => {

  const t = useTranslations("CreatePropertySuccessPage");
  const tCommon = useTranslations("Common");

  const { profile } = useAuth();

  const basePostPrice = DEFAULT_POST_PRICE
  const highlightPrice = profile?.highlightFee?.fee || 0;
  const totalPrice = basePostPrice + (property?.isHighlighted ? highlightPrice : 0);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-2xl mx-auto space-y-6">
        {/* Success Header */}
        <Card className="border-0 shadow-lg">
          <CardContent className="pt-8 pb-6 text-center">
            <div className="flex justify-center mb-4">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">{t("successHeader")}</h1>
            <p className="text-lg text-green-600 font-semibold mb-4">{t("successDescription")}</p>
            <div className="text-3xl font-bold text-gray-900 mb-2">{totalPrice.toLocaleString("vi-VN")} đ</div>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4">
              <div className="flex items-start gap-3">
                <Info className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                <p className="text-sm text-blue-800">
                  {t("moderationNotice")}
                </p>
              </div>
            </div>
            <div className="bg-teal-50 border border-teal-200 rounded-lg p-4 mt-4">
              <div className="flex items-start gap-3">
                <Info className="w-5 h-5 text-teal-600 mt-0.5 flex-shrink-0" />
                <p className="text-sm text-teal-800">
                  {t("transactionNotice")}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Transaction Details */}
        <Card className="border-0 shadow-lg">
          <CardHeader className="pb-4">
            <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
              <FileText className="w-5 h-5" />
              {t("transactionDetails")}
            </h2>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-600">{t("status")}</label>
                <div>
                  <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 border-yellow-200">
                    <Clock className="w-3 h-3 mr-1" />
                    {t("statusPending")}
                  </Badge>
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-600">{t("listingCode")}</label>
                <div className="font-mono text-sm bg-gray-50 px-3 py-2 rounded border">{property?.code || "N/A"}</div>
              </div>
            </div>

            <Separator />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-600 flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  {t("expirationDate")}
                </label>
                <div className="text-sm text-gray-900">{format(property?.expiresAt, "dd/MM/yyyy", { locale: vi }) || "N/A"}</div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-600 flex items-center gap-1">
                  <CreditCard className="w-4 h-4" />
                  {t("postFee")}
                </label>
                <div className="text-lg font-semibold text-gray-900">{totalPrice.toLocaleString("vi-VN")} đ</div>
              </div>
            </div>

            <Separator />

            {/* Additional Information */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-medium text-gray-900 mb-3">Thông tin bổ sung</h3>
              <div className="space-y-2 text-sm text-gray-600">
                <div className="flex justify-between">
                  <span>{t("postType")}:</span>
                  <span className="font-medium">{tCommon(`propertyPostType_${property?.postType}`) || "N/A"}</span>
                </div>
                <div className="flex justify-between">
                  <span>{t("postDate")}:</span>
                  <span className="font-medium">{format(new Date(property?.createdAt), "dd/MM/yyyy", { locale: vi }) || "N/A"}</span>
                </div>
                <div className="flex justify-between">
                  <span>{t("highlightFee")}:</span>
                  <span className="font-medium">{property?.isHighlighted ? t("yes") : t("no")} {highlightPrice.toLocaleString("vi-VN")} đ</span>
                </div>
                <div className="flex justify-between">
                  <span>{t("autoRenew")}:</span>
                  <span className="font-medium">{property?.isAutoRenew ? t("yes") : t("no")}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3">
          <Link href={`/user/bds/${property?.id}`} className="flex-1" >
            <Button className="w-full" size="lg">
              Xem tin đăng của tôi
            </Button>
          </Link>
          <Link href={`/user/bds/new`} className="flex-1">
            <Button className="w-full text-white bg-teal-500 hover:bg-teal-600" size="lg">
              Đăng tin mới
            </Button>
          </Link>
        </div>

        {/* Support Information */}
        <Card className="border-0 shadow-lg bg-gradient-to-r from-blue-600 to-indigo-600 text-white">
          <CardContent className="p-6">
            <h3 className="font-semibold mb-2">Cần hỗ trợ?</h3>
            <p className="text-blue-100 text-sm mb-4">
              Liên hệ với chúng tôi nếu bạn có bất kỳ thắc mắc nào về giao dịch này.
            </p>
            <div className="flex flex-col sm:flex-row gap-2">
              <Button variant="secondary" size="sm" className="bg-white text-blue-600 hover:bg-blue-50">
                Hotline: 1900-1234
              </Button>
              <Button variant="secondary" size="sm" className="bg-white text-blue-600 hover:bg-blue-50">
                Email: <EMAIL>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SuccessPageClient; 