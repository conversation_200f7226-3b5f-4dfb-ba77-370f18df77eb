# syntax=docker/dockerfile:1.5.0
# Multi-stage build for Next.js with Nginx and SSL

# Stage 1: Install dependencies
FROM node:22-alpine AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app
COPY package.json package-lock.json* ./
RUN npm ci

# Stage 2: Build the application
FROM node:22-alpine AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Build the Next.js application for static export
RUN npm run build

# Stage 3: Production with Nginx
FROM nginx:alpine AS runner
WORKDIR /app

# Install certbot for SSL
RUN apk add --no-cache certbot certbot-nginx openssl

# Copy built Next.js static files
COPY --from=builder /app/out /usr/share/nginx/html

# Create nginx configuration directory
RUN mkdir -p /etc/nginx/conf.d

# Copy Nginx configuration files
COPY nginx/nginx.conf /etc/nginx/nginx.conf
COPY nginx/default.conf /etc/nginx/conf.d/default.conf

# Create directories for SSL certificates and challenges
RUN mkdir -p /var/www/certbot
RUN mkdir -p /etc/letsencrypt

# Create a script to handle SSL certificate generation and renewal
COPY nginx/ssl-setup.sh /usr/local/bin/ssl-setup.sh
RUN chmod +x /usr/local/bin/ssl-setup.sh

# Create a startup script
COPY nginx/start.sh /usr/local/bin/start.sh
RUN chmod +x /usr/local/bin/start.sh

# Expose ports
EXPOSE 80 443

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/health || exit 1

# Start Nginx and SSL setup
CMD ["/usr/local/bin/start.sh"]
