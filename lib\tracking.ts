// lib/tracking.ts
import { v4 as uuidv4 } from 'uuid';
import Bowser from 'bowser';
import Cookies from 'js-cookie';

const API_BASE_URL = `${process.env.NEXT_PUBLIC_API_URL}/api/log`;

/**
 * Get or create session ID (stored in cookie)
 */
function getOrCreateSessionId(): string {
  let sessionId = Cookies.get('session_id');
  if (!sessionId) {
    sessionId = uuidv4();
    Cookies.set('session_id', sessionId, { expires: 1 }); // 1 day session
  }
  return sessionId;
}

/**
 * Get or create device ID (stored in localStorage)
 */
export function getOrCreateDeviceId(): string {
  if (typeof window === 'undefined') return 'unknown';

  let deviceId = localStorage.getItem('device_id');
  if (!deviceId) {
    deviceId = uuidv4();
    localStorage.setItem('device_id', deviceId);
  }
  return deviceId;
}

/**
 * Extract device information using Bowser
 */
export function getDeviceInfo() {
  if (typeof window === 'undefined') {
    return {
      userAgent: 'unknown',
      sessionId: 'unknown',
      deviceType: 'unknown',
      platform: 'unknown',
      browser: 'unknown',
      deviceId: 'unknown'
    };
  }

  const parser = Bowser.getParser(window.navigator.userAgent);
  return {
    userAgent: window.navigator.userAgent,
    sessionId: getOrCreateSessionId(),
    deviceType: parser.getPlatformType(true),
    platform: parser.getOSName(true),
    browser: parser.getBrowserName(true),
    deviceId: getOrCreateDeviceId(),
  };
}

/**
 * Send property view log to backend (PropertyViewLog)
 */
export async function logPropertyView(propertyId: string) {
  try {
    const device = getDeviceInfo();

    await fetch(`${API_BASE_URL}/property-view`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        propertyId,
        ...device
      }),
    });
  } catch (error) {
    console.error('[logPropertyView] Failed:', error);
  }
}

/**
 * Send property engagement event (PropertyEngagementEvents)
 */
export async function logPropertyEvent(
  propertyId: string,
  eventType:
    | 'click_phone'
    | 'chat'
    | 'favorite'
    | 'unfavorite'
    | 'submit_contact_form'
    | 'click_map'
    | 'share'
) {
  try {
    const device = getDeviceInfo();

    await fetch(`${API_BASE_URL}/property-event`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        propertyId,
        eventType,
        ...device
      }),
    });
  } catch (error) {
    console.error(`[logPropertyEvent:${eventType}] Failed:`, error);
  }
}

/**
 * Throttle helper (to prevent over-logging)
 */
export function throttle<T extends (...args: any[]) => void>(
  func: T,
  limitMs: number
): (...args: Parameters<T>) => void {
  let lastRun = 0;
  return function (...args: Parameters<T>) {
    const now = Date.now();
    if (now - lastRun >= limitMs) {
      lastRun = now;
      func(...args);
    }
  };
}

/**
 * Optional future: search impression log
 */
export async function logSearchImpression(propertyIds: string[]) {
  try {
    await fetch(`${API_BASE_URL}/search-impression`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ propertyIds }),
    });
  } catch (error) {
    console.error('[logSearchImpression] Failed:', error);
  }
}
