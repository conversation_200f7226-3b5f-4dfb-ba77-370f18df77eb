import React from "react";

/**
 * LoadingSpinner component for displaying loading states
 * @param {Object} props - Component props
 * @param {string} [props.text="Loading..."] - Text to display below spinner
 * @param {string} [props.className=""] - Additional classes for the container
 * @param {string} [props.size="md"] - Size of the spinner (sm, md, lg)
 * @param {boolean} [props.showText=true] - Whether to show the loading text
 * @returns {JSX.Element} LoadingSpinner component
 */
export function LoadingSpinner({
  text = "Loading...",
  className = "",
  size = "md",
  showText = true,
}) {
  // Size mappings
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-6 w-6",
    lg: "h-8 w-8",
  };

  // Height mappings
  const heightClasses = {
    sm: "h-20",
    md: "h-40",
    lg: "h-60",
  };

  // Border width mappings
  const borderClasses = {
    sm: "border-2",
    md: "border-2",
    lg: "border-3",
  };

  return (
    
    <div className={`flex flex-col justify-center items-center ${heightClasses[size]} ${className}`}>
      <div className={`animate-spin ease-linear ${sizeClasses[size]} ${borderClasses[size]} border-primary rounded-full border-t-transparent`}></div>
      {showText && <p className="text-sm text-gray-500 mt-3">{text}</p>}
    </div>
  );
} 