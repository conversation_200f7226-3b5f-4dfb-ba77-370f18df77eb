import { RotateCw } from "lucide-react";
import { useTranslations } from "next-intl";

export default function ButtonLoading({ ...props }) {
  const { showLoading, type, title } = props;
  const t = useTranslations("Common");
  return (
    <button
      type={type}
      disabled={showLoading}
      className="
        cursor-pointer
        group relative w-full flex items-center justify-center
        py-2.5 px-4 
        border border-transparent
        text-lg font-semibold
        rounded-md text-white
        bg-teal-600 hover:bg-teal-700
        focus:outline-hidden focus:ring-2 focus:ring-teal-500 focus:ring-offset-2
        transition-all duration-200 ease-in-out
        disabled:opacity-60 disabled:cursor-not-allowed"
    >
      {showLoading ? (
        <>
          <RotateCw className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" />
          {t("loading")}
        </>
      ) : (
        <>{title}</>
      )}
    </button>
  );
}
