# Technical Design Document: Property Detail Feature

## 1. Overview

The Property Detail feature allows users to view detailed information about a property in two ways:
1. Through a modal popup when clicking on a property in the PropertyList component
2. Through a dedicated page when accessing the property URL directly or opening in a new tab

## 2. Requirements

### 2.1 Functional Requirements

* As a user, I want to click on a property in the PropertyList to view its details in a modal popup
* As a user, I want to right-click or open a property link in a new tab to view its details in a dedicated page
* As a user, I want to see all property information including:
  - Basic details (name, price, area, etc.)
  - Location details with map visualization
  - Property images
  - Property specifications
  - Owner information
* As a user, I want to see nearby properties in a carousel format
* As a user, I want to interact with the property map to understand its location

### 2.2 Non-Functional Requirements

* The modal/page should load property details within 2 seconds
* The map should be interactive and responsive
* The UI should be consistent between modal and page views
* The feature should work seamlessly on both desktop and mobile devices
* The carousel should be optimized for performance with lazy loading

## 3. Technical Design

### 3.1. Data Model Changes

No database changes required. We'll use the existing PropertyDto model from the API.

### 3.2. UI Changes

#### 3.2.1 New Components

1. PropertyDetailModal (`components/property/PropertyDetailModal.jsx`)
   - Reusable modal component for property details
   - Contains all property information sections
   - Handles both modal and page view modes

2. PropertyMapSection (`components/property/PropertyMapSection.jsx`)
   - Displays the property location on Goong Maps
   - Shows property marker and nearby area
   - Interactive map controls

3. PropertyImageGallery (`components/property/PropertyImageGallery.jsx`)
   - Displays property images in a gallery format
   - Supports image zoom and navigation
   - Responsive grid layout

4. NearbyPropertiesCarousel (`components/property/NearbyPropertiesCarousel.jsx`)
   - Shows nearby properties in a carousel
   - Lazy loads property cards
   - Smooth scrolling navigation

#### 3.2.2 Component Structure

```jsx
PropertyDetailModal/
├── PropertyImageGallery
├── PropertyInfoSection
│   ├── BasicInfo
│   ├── Specifications
│   └── Description
├── PropertyMapSection
├── OwnerInfoSection
└── NearbyPropertiesCarousel
```

### 3.3. Logic Flow

```mermaid
sequenceDiagram
    participant User
    participant PropertyList
    participant PropertyDetailModal
    participant API
    participant MapService

    User->>PropertyList: Click property
    PropertyList->>PropertyDetailModal: Open modal
    PropertyDetailModal->>API: Fetch property details
    API-->>PropertyDetailModal: Return property data
    PropertyDetailModal->>MapService: Initialize map
    PropertyDetailModal->>API: Fetch nearby properties
    API-->>PropertyDetailModal: Return nearby data
    PropertyDetailModal->>User: Display complete UI
```

### 3.4. Dependencies

* Goong Maps SDK for map visualization
* React Query for data fetching and caching
* Framer Motion for smooth animations
* React PhotoSwipe for image gallery
* Swiper for carousel functionality

### 3.5. API Integration

1. Property Details API:
```typescript
GET /api/Property/{propertyId}
Response: PropertyDto
```

2. Nearby Properties API:
```typescript
GET /api/Property/search
Parameters:
  - latitude: number
  - longitude: number
  - radius: number (in kilometers)
  - pageSize: number
Response: PropertyDtoPagedResultDto
```

### 3.6. Security Considerations

* Property details are public and don't require authentication
* API calls should be rate-limited to prevent abuse
* Map API key should be properly secured

### 3.7. Performance Considerations

* Implement lazy loading for images and map
* Cache property details using React Query
* Optimize carousel rendering with virtualization
* Use image optimization for property photos

## 4. Testing Plan

### 4.1 Unit Tests
* Test property data transformation
* Test map initialization
* Test carousel navigation
* Test modal/page view switching

### 4.2 Integration Tests
* Test API integration
* Test map integration
* Test image gallery functionality
* Test responsive behavior

### 4.3 E2E Tests
* Test complete user flow
* Test different viewport sizes
* Test browser compatibility

## 5. Open Questions

* What should be the radius for nearby properties?
* Should we implement property sharing functionality?
* Should we add property contact form in the detail view?
* How should we handle property status changes in real-time?

## 6. Alternatives Considered

1. Using a different map service (Google Maps)
   - Rejected due to licensing costs and Vietnam-specific requirements

2. Using a different image gallery library
   - Rejected due to performance and customization limitations

3. Using server-side rendering for the detail page
   - Rejected to maintain consistency with modal view and reduce complexity 