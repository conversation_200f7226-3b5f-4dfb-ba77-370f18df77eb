"use client";

import { useEffect, useRef } from "react";
import goongjs from "@goongmaps/goong-js";
import "@goongmaps/goong-js/dist/goong-js.css";

const isBrowser = typeof window !== "undefined";

const DetailMap = ({ property, center }) => {
  const mapContainerRef = useRef(null);
  const mapRef = useRef(null);

  useEffect(() => {
    if (!isBrowser || !mapContainerRef.current || !center) {
      if (mapRef.current) {
        mapRef.current.remove();
        mapRef.current = null;
      }
      return;
    }

    goongjs.accessToken = `${process.env.NEXT_PUBLIC_GOONG_MAPTILES_KEY}`;

    const map = mapRef.current;

    if (!map) {
      // Khởi tạo bản đồ lần đầu tiên với vị trí center nhận được qua prop
      const goongMap = new goongjs.Map({
        container: mapContainerRef.current,
        style: "https://tiles.goong.io/assets/goong_map_web.json",
        center: [center.longitude, center.latitude],
        zoom: 15,
      });

      mapRef.current = goongMap;
    } else {
      // Nếu bản đồ đã được khởi tạo, chỉ cập nhật vị trí trung tâm nếu prop center thay đổi
      const currentCenter = mapRef.current.getCenter();
      const currentZoom = mapRef.current.getZoom();

      const centerHasChanged =
        currentCenter.lat !== center.latitude || currentCenter.lng !== center.longitude;
      const zoomHasChanged = currentZoom !== zoom;

      if (centerHasChanged || zoomHasChanged) {
        map.flyTo({
          center: [center.longitude, center.latitude],
          zoom: zoom,
          essential: true,
        });
      } else {
        console.log(
          "GoongMap Effect: Prop center/zoom không đổi so với trạng thái hiện tại, BỎ QUA cập nhật bản đồ."
        );
      }
    }

    return () => {
      if (mapRef.current) {
        mapRef.current.remove();
        mapRef.current = null;
      }
    };
  }, [center]);

  // Effect dọn dẹp bản đồ cuối cùng khi unmount
  useEffect(() => {
    return () => {
      if (mapRef.current) {
        mapRef.current.remove();
        mapRef.current = null;
      }
    };
  }, []);

  // --- Effect xử lý Marker ---
  useEffect(() => {
    if (!mapRef.current || !property) {
      return;
    }

    const map = mapRef.current;

    // Xóa marker cũ
    if (map._currentMarkers) {
      map._currentMarkers.forEach((marker) => marker.remove());
    }
    map._currentMarkers = [];

    if (property) {
      if (property.latitude !== undefined && property.longitude !== undefined) {
        try {
          // --- TẠO PHẦN TỬ DOM TÙY CHỈNH CHO MARKER ---
          const el = document.createElement("div");
          el.className = "marker";
          el.style.backgroundImage = "url(/detail_marker.png)";
          el.style.width = "50px";
          el.style.height = "50px";
          el.style.backgroundSize = "cover";
          el.style.cursor = "pointer";

          const marker = new goongjs.Marker(el, { anchor: "bottom" })
            .setLngLat([property.longitude, property.latitude])
            .addTo(map);

          map._currentMarkers.push(marker);
        } catch (markerError) {
          console.error(
            `GoongMap Effect Markers: Lỗi khi tạo hoặc thêm marker:`,
            property,
            markerError
          );
        }
      }
    } else {
      console.log("GoongMap Effect Marker: Không có marker để thêm.");
    }

    // Cleanup cho markers (xóa markers khi prop markers thay đổi hoặc unmount)
    return () => {
      if (mapRef.current && mapRef.current._currentMarkers) {
        mapRef.current._currentMarkers.forEach((marker) => marker.remove());
      }
    };
  }, [property]); // Dependencies: Chỉ chạy lại khi prop markers thay đổi

  return (
    <div className={`w-full h-[350px] relative z-0 opacity-75 cursor-not-allowed`}>
      <div ref={mapContainerRef} className="w-full h-full" />
    </div>
  );
};

export default DetailMap;
