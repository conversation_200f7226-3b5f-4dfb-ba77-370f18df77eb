export async function GET(req) {
  const { searchParams } = new URL(req.url);
  const input = searchParams.get("input");

  if (!input) {
    return Response.json({ error: "Missing input query" }, { status: 400 });
  }

  try {

    const apiURl = `https://rsapi.goong.io/Place/AutoComplete?api_key=${process.env.GOONG_GEO_API_KEY}&input=${encodeURIComponent(input)}&limit=15&more_compound=true`;
    const response = await fetch(
      apiURl
    );

    if (!response.ok) {
      let errorDetails = {
        status: response.status,
        statusText: response.statusText,
        url: apiURl
      };

      try {
        const errorBody = await response.text();
        errorDetails.responseBody = errorBody;
      } catch (parseError) {
        errorDetails.responseBody = "Could not parse error response";
      }

      const error = new Error(`Failed to fetch address suggestions: ${response.status} ${response.statusText}`);
      error.details = errorDetails;
      throw error;
    }

    const data = await response.json();
    return Response.json(data, { status: 200 });
  } catch (error) {
    console.error("Address suggestions API error:", {
      message: error.message,
      details: error.details,
      stack: error.stack
    });
    
    // Return detailed error for development, generic message for production
    const errorResponse = {
      error: error.message,
      ...(process.env.NODE_ENV === 'development' && { details: error.details })
    };
    
    return Response.json(errorResponse, { status: 500 });
  }
}
