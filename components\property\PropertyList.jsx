"use client";
import { memo, useState } from "react";
import { Loader2, X } from "lucide-react";
import PropertyCard from "@/components/property/PropertyCard";
import Pagination from "@/components/property/Pagination";
import { useTranslations } from "next-intl";
import LoginDialog from "@/components/auth/LoginDialog";
import { useAuth } from "@/contexts/AuthContext";

// Main PropertyList component
function PropertyList({
  properties = [],
  loading = false,
  onPropertySelect,
  pagination = {
    totalCount: 0,
    pageCount: 1,
    currentPage: 1,
    pageSize: 30,
    hasNextPage: false,
    hasPreviousPage: false,
  },
  onPageChange,
  favorites = {},
  onToggleFavorite,
}) {
  const [showLoginDialog, setShowLoginDialog] = useState(false);
  const t = useTranslations("PropertyList");
  const { isLoggedIn } = useAuth();

  // Handle toggling favorite status
  const handleToggleFavorite = (propertyId, isFavorite) => {
    if (!isLoggedIn) {
      setShowLoginDialog(true);
      return;
    }

    // Call the parent's onToggleFavorite function
    if (onToggleFavorite) {
      onToggleFavorite(propertyId, isFavorite);
    }
  };

  if (loading) {
    return (
      <aside className="w-full md:w-2/5 p-4 overflow-y-auto border-l border-l-stone-300 flex items-center justify-center h-[calc(100vh-227px)]">
        <div className="flex flex-col items-center justify-center p-8">
          <Loader2 className="h-8 w-8 text-teal-500 animate-spin mb-4" />
          <p className="text-gray-500">{t("loading")}</p>
        </div>
      </aside>
    );
  }

  if (properties.length === 0) {
    return (
      <aside className="w-full md:w-2/5 p-4 overflow-y-auto border-l border-l-stone-300 flex items-center justify-center h-[calc(100vh-227px)]">
        <div className="text-center p-8">
          <h3 className="text-xl font-semibold text-gray-700 mb-2">{t("noResults")}</h3>
          <p className="text-gray-500">{t("tryDifferentCriteria")}</p>
        </div>
      </aside>
    );
  }

  return (
    <>
      <aside className="w-full md:w-2/5 p-4 overflow-y-auto border-l border-l-stone-300 h-[calc(100vh-227px)]">
        <div className="mb-4">
          <h2 className="text-lg font-semibold">{t("searchResults", { count: pagination.totalCount })}</h2>
          <p className="text-sm text-gray-500">{t("pageInfo", { current: pagination.currentPage, total: pagination.pageCount })}</p>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          {properties.map((property) => (
            <PropertyCard
              key={property.id}
              property={property}
              onClick={onPropertySelect}
              onToggleFavorite={handleToggleFavorite}
              isFavorite={!!favorites[property.id]}
              isLoggedIn={isLoggedIn}
            />
          ))}
        </div>

        {/* Pagination UI */}
        {pagination.pageCount > 1 && <Pagination pagination={pagination} onPageChange={onPageChange} />}
      </aside>

      {/* Login Dialog */}
      <LoginDialog open={showLoginDialog} onOpenChange={setShowLoginDialog} />
    </>
  );
}

export default memo(PropertyList);
