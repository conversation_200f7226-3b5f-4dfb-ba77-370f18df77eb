import { Home, Wallet, Gift, User, Settings } from "lucide-react";
import { NOTIFICATION_CATEGORY } from "@/lib/enum";

/**
 * Get notification categories with icons and labels (replaces old notification types)
 * @param {Function} t - Translation function from useTranslations hook
 * @returns {Array} Array of notification category objects with id, label, and icon
 */
export const getNotificationTypes = (t) => [
  { id: NOTIFICATION_CATEGORY.LISTING, label: t('categoryListing'), icon: Home },
  { id: NOTIFICATION_CATEGORY.FINANCE, label: t('categoryFinance'), icon: Wallet },
  { id: NOTIFICATION_CATEGORY.PROMOTION, label: t('categoryPromotion'), icon: Gift },
  { id: NOTIFICATION_CATEGORY.ACCOUNT, label: t('categoryAccount'), icon: User },
  { id: NOTIFICATION_CATEGORY.MISCELLANEOUS, label: t('categoryMiscellaneous'), icon: Settings },
];

/**
 * Get notification type by ID
 * @param {Array} types - Array of notification types
 * @param {String} typeId - Notification type ID to find
 * @returns {Object|undefined} Notification type object or undefined if not found
 */
export const getNotificationTypeById = (types, typeId) => {
  return types.find(type => type.id === typeId);
};

/**
 * Get action text based on notification type
 * @param {Object} notification - Notification object
 * @param {Function} t - Translation function from useTranslations hook
 * @returns {String} Action text for the notification
 */
export const getNotificationActionText = (notification, t) => {
  // Import NOTIFICATION_TYPE for the switch statement
  const { NOTIFICATION_TYPE } = require("@/lib/enum");
  
  switch (notification.type) {
    // Listing notifications
    case NOTIFICATION_TYPE.LISTING_APPROVED:
      return t('actionTextListingApproved');
    case NOTIFICATION_TYPE.LISTING_REJECTED:
      return t('actionTextListingRejected');
    case NOTIFICATION_TYPE.LISTING_EXPIRED:
      return t('actionTextListingExpired');
    case NOTIFICATION_TYPE.NEW_CONTACT_REQUEST:
      return notification.relatedPropertyId ? t('actionTextContact') : t('actionTextContactDefault');
    
    // Finance notifications
    case NOTIFICATION_TYPE.WALLET_TOP_UP_SUCCESS:
    case NOTIFICATION_TYPE.WALLET_TOP_UP_FAILED:
    case NOTIFICATION_TYPE.SERVICE_PAYMENT_SUCCESS:
    case NOTIFICATION_TYPE.LOW_BALANCE_WARNING:
      return t('actionTextFinance');
    
    // Promotion notifications
    case NOTIFICATION_TYPE.NEW_DISCOUNT_AVAILABLE:
    case NOTIFICATION_TYPE.PROMOTIONAL_CODE_RECEIVED:
      return t('actionTextPromotion');
    
    // Account notifications
    case NOTIFICATION_TYPE.WELCOME_USER:
    case NOTIFICATION_TYPE.PASSWORD_RESET_REQUEST:
    case NOTIFICATION_TYPE.ACCOUNT_SECURITY_ALERT:
      return t('actionTextAccount');
    
    // Miscellaneous notifications
    case NOTIFICATION_TYPE.SYSTEM_MAINTENANCE:
    case NOTIFICATION_TYPE.FEATURE_ANNOUNCEMENT:
    case NOTIFICATION_TYPE.GENERAL_SYSTEM_MESSAGE:
      return t('actionTextSystem');
    
    default:
      return t('actionTextDefault');
  }
};

/**
 * Get notification category based on notification type
 * @param {String} notificationType - Notification type
 * @returns {String} Notification category
 */
export const getNotificationCategory = (notificationType) => {
  const { NOTIFICATION_TYPE, NOTIFICATION_CATEGORY } = require("@/lib/enum");
  
  switch (notificationType) {
    case NOTIFICATION_TYPE.LISTING_CREATED:
    case NOTIFICATION_TYPE.LISTING_APPROVED:
    case NOTIFICATION_TYPE.LISTING_REJECTED:
    case NOTIFICATION_TYPE.LISTING_EXPIRED:
    case NOTIFICATION_TYPE.NEW_CONTACT_REQUEST:
      return NOTIFICATION_CATEGORY.LISTING;
    
    case NOTIFICATION_TYPE.WALLET_TOP_UP_SUCCESS:
    case NOTIFICATION_TYPE.WALLET_TOP_UP_FAILED:
    case NOTIFICATION_TYPE.SERVICE_PAYMENT_SUCCESS:
    case NOTIFICATION_TYPE.LOW_BALANCE_WARNING:
      return NOTIFICATION_CATEGORY.FINANCE;
    
    case NOTIFICATION_TYPE.NEW_DISCOUNT_AVAILABLE:
    case NOTIFICATION_TYPE.PROMOTIONAL_CODE_RECEIVED:
      return NOTIFICATION_CATEGORY.PROMOTION;
    
    case NOTIFICATION_TYPE.WELCOME_USER:
    case NOTIFICATION_TYPE.PASSWORD_RESET_REQUEST:
    case NOTIFICATION_TYPE.ACCOUNT_SECURITY_ALERT:
      return NOTIFICATION_CATEGORY.ACCOUNT;
    
    case NOTIFICATION_TYPE.SYSTEM_MAINTENANCE:
    case NOTIFICATION_TYPE.FEATURE_ANNOUNCEMENT:
    case NOTIFICATION_TYPE.GENERAL_SYSTEM_MESSAGE:
      return NOTIFICATION_CATEGORY.MISCELLANEOUS;
    
    default:
      return NOTIFICATION_CATEGORY.MISCELLANEOUS;
  }
};

/**
 * Check if notification is actionable
 * @param {Object} notification - Notification object
 * @returns {Boolean} True if notification has action URL or related entities
 */
export const isActionableNotification = (notification) => {
  return notification.actionUrl || notification.relatedPropertyId || notification.relatedEntityId;
}; 