# YEZHome Frontend Deployment Configuration
# This file contains environment variables used in GitHub Actions workflows

# Server Configuration (Secrets)
DROPLET_IP=your.server.ip.address
DROPLET_USERNAME=deploy
DROPLET_SSH_KEY=your_private_ssh_key_content
PULL_PAT=your_github_token_or_pat

# Staging Environment Variables (Repository Variables)
STAGING_FRONTEND_PORT=3001
STAGING_API_URL=https://api.staging.yezhome.com
STAGING_INTERNAL_API_URL=https://internal-api.staging.yezhome.com
STAGING_FRONTEND_URL=https://staging.yezhome.com

# Production Environment Variables (Repository Variables)
PRODUCTION_FRONTEND_PORT=3000
PRODUCTION_API_URL=https://api.yezhome.com
PRODUCTION_INTERNAL_API_URL=https://internal-api.yezhome.com
PRODUCTION_FRONTEND_URL=https://yezhome.com
PRODUCTION_ANALYTICS_ID=your_analytics_id

# Container Configuration
CONTAINER_MEMORY_LIMIT=1g
CONTAINER_CPU_LIMIT=1.0

# Backup Configuration
BACKUP_RETENTION_DAYS=7
MAX_IMAGE_BACKUPS=5

# Health Check Configuration
HEALTH_CHECK_TIMEOUT=30
HEALTH_CHECK_RETRIES=5
HEALTH_CHECK_INTERVAL=30
