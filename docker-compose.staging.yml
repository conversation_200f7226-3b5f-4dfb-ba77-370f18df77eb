version: '3.8'

services:
  yezhome-frontend-staging:
    build:
      context: .
      dockerfile: Dockerfile.nginx
    container_name: yezhome-frontend-staging
    restart: always
    ports:
      - "8080:80"
      - "8443:443"
    environment:
      - DOMAIN=test.yezhome.vn
      - CERTBOT_EMAIL=${CERTBOT_EMAIL}
      - CERTBOT_STAGING=true  # Use Let's Encrypt staging for testing
    volumes:
      # Persistent storage for SSL certificates
      - frontend_ssl_certs_staging:/etc/letsencrypt
      - frontend_ssl_challenges_staging:/var/www/certbot
      # Optional: Custom nginx configurations
      - ./nginx/custom:/etc/nginx/custom:ro
    networks:
      - yezhome-frontend-staging-network
    labels:
      - "environment=staging"
      - "domain=test.yezhome.vn"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  frontend_ssl_certs_staging:
    name: yezhome_staging_frontend_ssl_certs
  frontend_ssl_challenges_staging:
    name: yezhome_staging_frontend_ssl_challenges

networks:
  yezhome-frontend-staging-network:
    name: yezhome-frontend-staging-network
    driver: bridge
