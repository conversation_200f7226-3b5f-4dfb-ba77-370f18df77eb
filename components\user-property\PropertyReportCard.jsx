"use client";

import { useState, useEffect, memo } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Eye, BarChart, Heart } from "lucide-react";
import { getPropertyReportById } from "@/app/actions/server/property"; // Import the server action
import { formatCurrency } from "@/lib/utils";
import { CardContent } from "@/components/ui/card"; // Use CardContent for consistent styling

// Cache for report data to avoid duplicate API calls
const reportCache = new Map();

const PropertyReportCard = memo(function PropertyReportCard({ propertyId, reportData: preloadedReportData }) {
  const [reportData, setReportData] = useState(preloadedReportData || null);
  const [isLoading, setIsLoading] = useState(!preloadedReportData);
  const [error, setError] = useState(null);

  useEffect(() => {
    // If we already have preloaded data, don't fetch
    if (preloadedReportData) {
      setReportData(preloadedReportData);
      setIsLoading(false);
      return;
    }

    // Check cache first
    if (reportCache.has(propertyId)) {
      const cachedData = reportCache.get(propertyId);
      setReportData(cachedData);
      setIsLoading(false);
      return;
    }

    async function fetchReportData() {
      if (!propertyId) return;

      setIsLoading(true);
      setError(null);
      try {
        const result = await getPropertyReportById(propertyId);
        if (result && result?.isSuccess) {
          const data = result?.data;
          setReportData(data);
          // Cache the result
          reportCache.set(propertyId, data);
        } else {
          setError(result?.message || "Lỗi tải báo cáo.");
          console.error("Error fetching report:", result?.message);
        }
      } catch (err) {
        setError("Lỗi kết nối.");
        console.error("Network or other error fetching report:", err);
      } finally {
        setIsLoading(false);
      }
    }

    fetchReportData();
  }, [propertyId, preloadedReportData]); // Re-fetch if propertyId changes

  if (isLoading) {
    return (
      <CardContent className="flex items-center justify-center p-4 text-xs text-gray-500 w-full md:w-1/4 md:shrink-0 border-b md:border-b-0 md:border-r min-h-[150px]">
        <LoaderCircle className="animate-spin h-5 w-5 mr-2" />
        Đang tải báo cáo...
      </CardContent>
    );
  }

  if (error) {
    return (
      <CardContent className="flex items-center justify-center p-4 text-xs text-red-600 w-full md:w-1/4 md:shrink-0 border-b md:border-b-0 md:border-r min-h-[150px]">
        {error}
      </CardContent>
    );
  }

  if (!reportData) {
    return (
      <CardContent className="flex items-center justify-center p-4 text-xs text-gray-500 w-full md:w-1/4 md:shrink-0 border-b md:border-b-0 md:border-r min-h-[150px]">
        Không có dữ liệu báo cáo.
      </CardContent>
    );
  }

  // Render the full report details
  return (
    <div className="grid gap-1 p-4 text-xs text-gray-600 w-full md:w-1/4 md:shrink-0 border-b md:border-b-0 md:border-r">
      {/* Stats */}
      <div className="grid grid-cols-3 gap-x-3 text-gray-800">
        <div className="flex items-center gap-1">
          <Eye className="w-4 h-4" />
          {reportData.views}
        </div>
        <div className="flex items-center gap-1">
          <BarChart className="w-4 h-4" />
          {reportData.impressions}
        </div>
        <div className="flex items-center gap-1">
          <Heart className="w-4 h-4" />
          {reportData.cartAdds ?? "N/A"}
        </div>
      </div>

      {/* Divider */}
      <hr className="my-2" />

      {/* Costs */}
      <div className="grid grid-rows-5 gap-y-1 text-gray-800 font-medium">
        <div className="flex items-center justify-between">
          <span>Đăng bài</span>
          {formatCurrency(reportData.postCost ?? 0)}
        </div>
        <div className="flex items-center justify-between">
          <span>Highlight ({reportData.highlightsCount ?? "0"})</span>
          {formatCurrency(reportData.highlightCost ?? 0)}
        </div>
        <div className="flex items-center justify-between">
          <span>Gia hạn ({reportData.renewalsCount ?? "0"})</span>
          {formatCurrency(reportData.renewalCost ?? 0)}
        </div>
        <hr className="my-1" />
        {/* Total */}
        <div className="flex justify-between items-center text-gray-900 font-semibold">
          <span>Tổng cộng</span>
          {formatCurrency(reportData.totalCost ?? 0)}
        </div>
      </div>
    </div>
  );
});

export default PropertyReportCard;
