"use client";

import { FormField, FormItem, FormLabel, FormControl } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useTranslations } from "next-intl";

const PropertyVideoInput = ({ form, isFormDisabled }) => {
  const t = useTranslations("PropertyForm");

  return (
    <FormField
      control={form.control}
      name="videoUrl"
      className="mt-6"
      render={({ field }) => (
        <FormItem>
          <FormLabel className="mt-6">{t("propertyVideoLink")}</FormLabel>
          <FormControl>
            <Input 
              placeholder={t("propertyVideoLinkPlaceholder")} 
              {...field} 
              disabled={isFormDisabled}
            />
          </FormControl>
        </FormItem>
      )}
    />
  );
};

export default PropertyVideoInput;
