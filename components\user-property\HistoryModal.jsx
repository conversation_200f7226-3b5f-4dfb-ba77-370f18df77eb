import { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { getPropertyStatusHistory } from "@/app/actions/server/property";
import { useToast } from "@/hooks/use-toast";
import { format } from "date-fns";
import { Card } from "@/components/ui/card";
import { useTranslations } from "next-intl";
import BadgeStatus from "../layout/BadgeStatus";

export default function HistoryModal({ propertyId, open, onClose }) {
  const { toast } = useToast();
  const [history, setHistory] = useState([]);
  const [loading, setLoading] = useState(false);
  const tCommon = useTranslations("Common");

  useEffect(() => {
    if (open && propertyId) {
      loadHistory();
    }
  }, [open, propertyId]);

  const loadHistory = async () => {
    setLoading(true);
    try {
      const response = await getPropertyStatusHistory(propertyId);
      if (response?.isSuccess) {
        setHistory(response.data);
      } else {
        toast({
          title: "Lỗi",
          description: response.message || "Không thể tải lịch sử hoạt động",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error loading history:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-[600px]" onPointerDownOutside={(e) => e.preventDefault()}>
        <DialogHeader>
          <DialogTitle>Lịch sử hoạt động</DialogTitle>
        </DialogHeader>

        <ScrollArea className="flex-1 w-full rounded-md border max-h-[400px]">
          {loading ? (
            <div className="text-center text-gray-500 py-8">Đang tải...</div>
          ) : (
            <div className="p-4 space-y-4">
              {history.length > 0 ? (
                history.map((item, index) => (
                  <div className="relative pl-4" key={`history-${index}`}>
                    <div className="absolute left-4 top-0 h-full w-0.5 bg-gray-200"></div>

                    <div className="relative mb-6">
                      <div className="absolute -left-1.5 top-0 flex h-4 w-4 items-center justify-center rounded-full bg-white ring-8 ring-white">
                        <span className="h-2 w-2 rounded-full bg-green-500"></span>{" "}
                      </div>
                      <div className="ml-4">
                        <p className="text-xs text-gray-500">{format(new Date(item.changedAt), "dd/MM/yyyy HH:mm")}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <BadgeStatus status={item.status} statusText={tCommon(`propertyStatus_${item?.status}`)} />
                          {item.comment && <p className="text-sm text-gray-600 mt-1">{item.comment}</p>}
                        </div>
                      </div>
                    </div>
                  </div>
                  // <Card key={index} className="p-4 relative">
                  //   <div className="flex items-start gap-4">
                  //     <div className="min-w-[120px] text-sm text-gray-500">
                  //       {format(new Date(item.changedAt), "dd/MM/yyyy HH:mm")}
                  //     </div>
                  //     <div className="flex-1">
                  //       <div className="font-medium">{item.status}</div>
                  //       {item.comment && (
                  //         <p className="text-sm text-gray-600 mt-1">{item.comment}</p>
                  //       )}
                  //     </div>
                  //   </div>
                  // </Card>
                ))
              ) : (
                <div className="text-center text-gray-500 py-8">Chưa có lịch sử hoạt động nào</div>
              )}
            </div>
          )}
        </ScrollArea>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Đóng
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
