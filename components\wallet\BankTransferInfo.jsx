"use client";

import { But<PERSON> } from "@/components/ui/button";
import { AlertCircle, Copy } from "lucide-react";
import { toast } from "@/hooks/use-toast";

export default function BankTransferInfo({ profile, bankInfo, t }) {
  // Function to copy text to clipboard
  const handleCopyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    toast({
      title: t("copySuccessToastTitle"),
      description: t("copySuccessToastDesc"),
    });
  };
  
  return (
    <div className="mt-4 space-y-4 bg-gray-50 p-4 rounded-md">
      <div className="text-sm font-medium text-gray-800">{t("bankTransferTitle")}</div>

      <div className="space-y-3">
        <div className="flex justify-between">
          <span className="text-gray-600">{t("bankLabel")}</span>
          <span className="font-medium">{bankInfo.bankName}</span>
        </div>

        <div className="flex justify-between items-center">
          <span className="text-gray-600">{t("accountNumberLabel")}</span>
          <div className="flex items-center">
            <span className="font-medium mr-2">{bankInfo.accountNumber}</span>
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={() => handleCopyToClipboard(bankInfo.accountNumber)}
            >
              <Copy className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="flex justify-between">
          <span className="text-gray-600">{t("accountNameLabel")}</span>
          <span className="font-medium">{bankInfo.accountName}</span>
        </div>

        <div className="flex justify-between">
          <span className="text-gray-600">{t("branchLabel")}</span>
          <span className="font-medium">{bankInfo.branch}</span>
        </div>

        <div className="flex justify-between items-center">
          <span className="text-gray-600">{t("transferContentLabel")}</span>
          <div className="flex items-center">
            <span className="font-medium mr-2">{profile?.user?.transferCode}</span>
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={() => handleCopyToClipboard(profile?.user?.transferCode)}
            >
              <Copy className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="bg-amber-50 p-3 rounded-md border border-amber-200 text-amber-800 text-sm">
          <div className="flex">
            <AlertCircle className="h-5 w-5 mr-2 shrink-0" />
            <div>
              <p className="font-medium">{t("importantNoteTitle")}</p>
              <ul className="list-disc pl-5 mt-1 space-y-1">
                <li>{t("note1")}</li>
                <li>{t("note2")}</li>
                <li>{t("note3")}</li>
                <li>{t("note4")}</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
