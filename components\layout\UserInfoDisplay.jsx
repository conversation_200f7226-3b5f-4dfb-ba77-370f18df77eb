"use client";
import { useAuth } from "@/contexts/AuthContext";
import { formatCurrency } from "@/lib/utils";
import { Wallet, User, RefreshCw } from "lucide-react";
import { useTranslations } from 'next-intl';
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export default function UserInfoDisplay() {
  const { profile, loading, refreshProfile } = useAuth();
  const walletInfo = profile?.user?.wallet;
  const t = useTranslations('UserInfo');

  if (!profile) return null;

  return (
    <div className="flex items-center space-x-4">
      {/* User Name */}
      <div className="flex items-center text-sm">
        <User className="h-4 w-4 mr-1 text-gray-500" />
        <span className="font-medium">{profile?.User?.fullName}</span>
      </div>

      {/* Wallet Balance */}
      {walletInfo && (
        <div className="flex items-center text-sm">
          <Wallet className="h-4 w-4 mr-1 text-gray-500" />
          <span className="font-medium">{formatCurrency(walletInfo.balance)}</span>
        </div>
      )}

      {/* Refresh Button */}
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              onClick={refreshProfile}
              disabled={loading}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin text-coral-500' : ''}`} />
              <span className="sr-only">{t('refresh')}</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>{t('refreshUserData')}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
}
