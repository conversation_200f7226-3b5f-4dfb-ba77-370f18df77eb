export const handleErrorResponse = (success, fieldValues = null, message = null) => ({
  success,
  message,
  fieldValues,  
  timestamp: new Date().toISOString()
});

export const logError = (serviceName, error, additionalContext = {}) => {
  console.error(`[${serviceName}] Error:`, {
    message: error.message,
    stack: error.stack,
    ...additionalContext,
    timestamp: new Date().toISOString()
  });
};