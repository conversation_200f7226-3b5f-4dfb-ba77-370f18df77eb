"use client";

import { useState, useEffect, useCallback } from "react";
import { checkFavoriteStatus } from "@/app/actions/server/userFavorite";
import { useAuth } from "@/contexts/AuthContext";
import PropertyDetailClient from "./PropertyDetailClient";
import { logPropertyView } from "@/lib/tracking";

interface PropertyDetailWrapperProps {
  property: any;
}

export default function PropertyDetailWrapper({ property }: PropertyDetailWrapperProps) {
  const [isFavorite, setIsFavorite] = useState(false);
  const { isLoggedIn } = useAuth();

  // Check favorite status when component mounts and user is logged in
  useEffect(() => {
    const fetchFavoriteStatus = async () => {
      if (!isLoggedIn || !property?.id) return;

      try {
        const result = await checkFavoriteStatus([property.id]);

        if (result?.isSuccess && result.data.length > 0) {
          setIsFavorite(result.data[0].isFavorite);
        }
      } catch (error) {
        console.error("Error fetching favorite status:", error);
      }
    };

    if (isLoggedIn) {
      fetchFavoriteStatus();
    }
  }, [property?.id, isLoggedIn]);

  useEffect(() => {
    if (property &&property?.id) {
      logPropertyView(property.id);
    }
  }, [property?.id]);

  // Handle toggling favorite status
  const handleToggleFavorite = useCallback((propertyId: string, newIsFavorite: boolean) => {
    setIsFavorite(newIsFavorite);

    // Dispatch a custom event that the navbar can listen to
    window.dispatchEvent(new CustomEvent("favorites-changed"));
  }, []);

  return (
    <PropertyDetailClient
      property={property}
      isFavorite={isFavorite}
      onToggleFavorite={handleToggleFavorite}
    />
  );
}
