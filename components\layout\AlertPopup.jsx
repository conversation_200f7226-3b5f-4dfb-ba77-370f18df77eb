"use client";
import { logout } from "@/app/actions/server/authenticate";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useAlert } from "@/contexts/AlertContext";

function AlertPopup() {
  const { alert, closeAlert } = useAlert();

  if (!alert) return null;

  const handleCancel = () => {
    alert.onCancel?.();
    closeAlert();
  };

  const handleConfirm = async () => {
    alert.onConfirm?.();
    closeAlert();
    if (
      alert?.errorType === "token_expired" ||
      alert?.errorType === "unauthorized" ||
      alert?.errorType === "no_token"
    ) {
      await logout();
    }
  };

  return (
    <AlertDialog open={!!alert}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{alert.title}</AlertDialogTitle>
          <AlertDialogDescription>{alert.message}</AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          {alert.hasCancel && <AlertDialogCancel onClick={handleCancel}>Cancel</AlertDialogCancel>}
          <AlertDialogAction onClick={handleConfirm}>OK</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

export default AlertPopup;
