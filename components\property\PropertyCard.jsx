"use client";
import { memo, useEffect, useState } from "react";
import Image from "next/image";
import { ArrowR<PERSON>, Heart, MapPin, BedDouble, Bath, Square } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { addToFavorites, removeFromFavorites } from "@/app/actions/server/userFavorite";
import { useTranslations } from "next-intl";
import { PostType, PropertyEventType } from "@/lib/enum";
import { logPropertyEvent } from "@/lib/tracking";

const PropertyCard = memo(({ property, onClick, onToggleFavorite, isFavorite = false, isLoggedIn }) => {
  const [favorite, setFavorite] = useState(isFavorite);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const t = useTranslations("PropertyCard");

  // Update local favorite state when prop changes
  useEffect(() => {
    setFavorite(isFavorite);
  }, [isFavorite]);

  const handleFavoriteClick = async (e) => {
    e.stopPropagation();

    if (!isLoggedIn) {
      onToggleFavorite(property.id, false);
      return;
    }

    setIsLoading(true);
    try {
      const newFavoriteStatus = !favorite;

      if (newFavoriteStatus) {
        logPropertyEvent(property.id, PropertyEventType.FAVORITE);
      } else {
        logPropertyEvent(property.id, PropertyEventType.UNFAVORITE);
      }

      const result = newFavoriteStatus ? await addToFavorites(property.id) : await removeFromFavorites(property.id);

      if (result?.isSuccess) {
        setFavorite(newFavoriteStatus);
        onToggleFavorite(property.id, newFavoriteStatus);
        toast({
          title: newFavoriteStatus ? t("addedToFavorites") : t("removedFromFavorites"),
          description: newFavoriteStatus ? t("addedToFavoritesDesc") : t("removedFromFavoritesDesc"),
          variant: "default",
        });
      } else {
        toast({
          title: t("errorOccurred"),
          description: result.message || t("cannotUpdateFavorite"),
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error toggling favorite:", error);
      toast({
        title: t("errorOccurred"),
        description: t("cannotUpdateFavorite"),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div
      className="bg-white rounded-md shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 hover:cursor-pointer property-card"
      onClick={() => onClick(property)}
    >
      <div className="relative">
        <Image
          src={property.propertyMedia?.[0]?.mediaURL || "/placeholder.svg"}
          alt={property.name}
          width={400}
          height={200}
          className="w-full h-48 object-cover"
          loading="lazy"
        />
        <div className="absolute top-2 right-2 bg-teal-500 text-white px-2 py-1 rounded text-xs font-semibold">
          {property.postType === PostType.SALE ? t("forSale") : t("forRent")}
        </div>

        {/* Favorite heart button */}
        <button
          className={`absolute top-2 left-2 p-2 rounded-full ${
            favorite ? "bg-coral-500 text-white" : "bg-white text-coral-500"
          } shadow-md transition-colors duration-300`}
          onClick={handleFavoriteClick}
          disabled={isLoading}
        >
          <Heart className={`h-5 w-5 ${isLoading ? "animate-pulse" : ""}`} fill={favorite ? "currentColor" : "none"} />
        </button>
      </div>
      <div className="p-4">
        <h3 className="text-sm font-semibold text-gray-800 mb-2 line-clamp-2">{property.name}</h3>
        <p className="text-lg font-bold text-teal-600 mb-2">
          {property.price.toLocaleString("vi-VN")} VNĐ
          {property.postType === "rent" && `/${t("month", { defaultValue: "tháng" })}`}
        </p>
        <p className="text-sm text-gray-500 mb-2 flex items-center">
          <MapPin className="h-4 w-4 mr-1" />
          <span className="line-clamp-1">{property.addressSelected || property.address}</span>
        </p>
        <div className="flex justify-between text-sm text-gray-500 mb-3">
          <div className="flex items-center">
            <Square className="h-3 w-3 mr-1" />
            <span>{property.area || "__"} m²</span>
          </div>
          <div className="flex items-center">
            <BedDouble className="h-3 w-3 mr-1" />
            <span>
              {property.rooms || "__"} {t("bedrooms")}
            </span>
          </div>
          <div className="flex items-center">
            <Bath className="h-3 w-3 mr-1" />
            <span>
              {property.toilets || "__"} {t("bathrooms")}
            </span>
          </div>
        </div>
        <Button
          type="button"
          variant="outline"
          className="w-full border-teal-500 text-teal-500 hover:bg-teal-50"
          onClick={(e) => {
            e.stopPropagation();
            onClick(property);
          }}
        >
          {t("details")}
          <ArrowRight className="ml-2 w-4 h-4" />
        </Button>
      </div>
    </div>
  );
});

PropertyCard.displayName = "PropertyCard";

export default PropertyCard;
