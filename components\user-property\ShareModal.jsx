"use client";
import { useState } from "react"; 
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Copy, Check } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useTranslations } from "next-intl";
import Image from "next/image";

// Import next-share components
import {
  FacebookShareButton,
  FacebookIcon,
  TwitterShareButton,
  TwitterIcon,
  LinkedinShareButton,
  LinkedinIcon,
  TelegramShareButton,
  TelegramIcon,
  WhatsappShareButton,
  WhatsappIcon,
  RedditShareButton,
  RedditIcon,
  EmailShareButton,
  EmailIcon,
  PinterestShareButton,
  PinterestIcon,
  ViberShareButton,
  ViberIcon,
} from "next-share";

const ShareModal = ({ open, onClose, property }) => {
  const { toast } = useToast();
  const [copied, setCopied] = useState(false);
  const t = useTranslations("PropertyShare");

  // Create a shareable URL for the property
  const shareUrl = typeof window !== "undefined" ? `${window.location.origin}/bds/${property.id}` : `/bds/${property.id}`;

  // Handle copy to clipboard
  const handleCopyLink = () => {
    navigator.clipboard
      .writeText(shareUrl)
      .then(() => {
        setCopied(true);
        toast({
          description: t("copySuccess"),
          className: "bg-teal-600 text-white",
        });
        setTimeout(() => setCopied(false), 2000);
      })
      .catch((err) => {
        console.error("Could not copy text: ", err);
        toast({
          description: t("copyError"),
          variant: "destructive",
        });
      });
  };

  // Share title and description
  const shareTitle = `${property.name} | YEZ Home`;
  const shareDescription = `${t("viewProperty")}: ${property.name} - ${JSON.parse(property?.placeData || "{}")?.result?.formatted_address || ""}`;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{t("title")}</DialogTitle>
          <DialogDescription>{t("description")}</DialogDescription>
        </DialogHeader>

        {/* Property info */}
        <div className="p-4 border rounded-md mb-4">
          <div className="flex items-center gap-2">
            <Image
              src={property.propertyMedia?.[0]?.mediaURL || "/placeholder.svg"}
              alt={property.name}
              width={100}
              height={100}
              className="w-full object-cover rounded-md"
              loading="lazy"
            />
            <div className="flex flex-col items-center gap-2">
              <h3 className="font-medium text-sm mb-1">{property.name}</h3>

              <p className="text-lg font-bold text-teal-600 mb-2">
                {property.price.toLocaleString("vi-VN")} VNĐ
                {property.postType === "rent" && `/${t("month", { defaultValue: "tháng" })}`}
              </p>
            </div>
          </div>
        </div>

        {/* Social sharing buttons */}
        <div className="flex flex-wrap gap-3 justify-center mb-4">
          <FacebookShareButton url={shareUrl} quote={shareDescription} className="mx-1">
            <FacebookIcon size={40} round />
          </FacebookShareButton>

          <TwitterShareButton url={shareUrl} title={shareTitle} className="mx-1">
            <TwitterIcon size={40} round />
          </TwitterShareButton>

          <LinkedinShareButton url={shareUrl} title={shareTitle} className="mx-1">
            <LinkedinIcon size={40} round />
          </LinkedinShareButton>

          <TelegramShareButton url={shareUrl} title={shareTitle} className="mx-1">
            <TelegramIcon size={40} round />
          </TelegramShareButton>

          <WhatsappShareButton url={shareUrl} title={shareTitle} separator=":: " className="mx-1">
            <WhatsappIcon size={40} round />
          </WhatsappShareButton>

          <PinterestShareButton url={shareUrl} media={property.propertyMedia?.[0]?.mediaURL || ""} description={shareDescription} className="mx-1">
            <PinterestIcon size={40} round />
          </PinterestShareButton>

          <RedditShareButton url={shareUrl} title={shareTitle} className="mx-1">
            <RedditIcon size={40} round />
          </RedditShareButton>

          <ViberShareButton url={shareUrl} title={shareTitle} className="mx-1">
            <ViberIcon size={40} round />
          </ViberShareButton>

          <EmailShareButton url={shareUrl} subject={shareTitle} body={shareDescription} className="mx-1">
            <EmailIcon size={40} round />
          </EmailShareButton>
        </div>

        {/* Copy link section */}
        <div className="flex items-center space-x-2">
          <div className="grid flex-1 gap-2">
            <Input value={shareUrl} readOnly className="h-9" />
          </div>
          <Button size="sm" className="px-3 h-9" onClick={handleCopyLink}>
            {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
            <span className="sr-only">{t("copy")}</span>
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ShareModal;
