# Multilingual Actionable Notifications Implementation

## Overview
The notification system now supports multilingual action text for actionable notifications, providing a localized experience for both Vietnamese and English users.

## Language Keys Added

### Vietnamese (vi.json)
```json
{
  "UserNotificationsPage": {
    "navigationErrorTitle": "Lỗi điều hướng",
    "navigationErrorMessage": "<PERSON>hông thể mở liên kết. Vui lòng thử lại.",
    "actionTextContact": "Xem bất động sản",
    "actionTextContactDefault": "Xem chi tiết",
    "actionTextTransaction": "Xem giao dịch",
    "actionTextWalletUpdate": "Xem ví",
    "actionTextSystem": "Xem thông báo",
    "actionTextPromotion": "Xem khuyến mãi",
    "actionTextNews": "Đ<PERSON>c tin tức",
    "actionTextDefault": "Xem chi tiết"
  }
}
```

### English (en.json)
```json
{
  "UserNotificationsPage": {
    "navigationErrorTitle": "Navigation Error",
    "navigationErrorMessage": "Unable to open link. Please try again.",
    "actionTextContact": "View Property",
    "actionTextContactDefault": "View Details",
    "actionTextTransaction": "View Transaction",
    "actionTextWalletUpdate": "View Wallet",
    "actionTextSystem": "View Notification",
    "actionTextPromotion": "View Promotion",
    "actionTextNews": "Read News",
    "actionTextDefault": "View Details"
  }
}
```

## Implementation Details

### Updated Functions
1. **getActionText(notification)**: Now uses translation keys instead of hardcoded text
2. **handleNotificationClick()**: Uses translated error messages
3. **Legacy link support**: Uses translated default action text

### Translation Logic
```javascript
const getActionText = (notification) => {
  switch (notification.type?.toLowerCase()) {
    case 'contact':
      return notification.relatedPropertyId ? t('actionTextContact') : t('actionTextContactDefault');
    case 'transaction':
      return t('actionTextTransaction');
    case 'walletupdate':
      return t('actionTextWalletUpdate');
    case 'system':
      return t('actionTextSystem');
    case 'promotion':
      return t('actionTextPromotion');
    case 'news':
      return t('actionTextNews');
    default:
      return t('actionTextDefault');
  }
};
```

## Notification Type Mappings

| Notification Type | Vietnamese Action Text | English Action Text |
|-------------------|------------------------|---------------------|
| Contact (with property) | "Xem bất động sản" | "View Property" |
| Contact (default) | "Xem chi tiết" | "View Details" |
| Transaction | "Xem giao dịch" | "View Transaction" |
| WalletUpdate | "Xem ví" | "View Wallet" |
| System | "Xem thông báo" | "View Notification" |
| Promotion | "Xem khuyến mãi" | "View Promotion" |
| News | "Đọc tin tức" | "Read News" |
| Default/Fallback | "Xem chi tiết" | "View Details" |

## Error Messages

### Navigation Errors
- **Vietnamese**: "Lỗi điều hướng" / "Không thể mở liên kết. Vui lòng thử lại."
- **English**: "Navigation Error" / "Unable to open link. Please try again."

## Usage Examples

### Contact Notification
```javascript
// Vietnamese locale
{
  type: "Contact",
  relatedPropertyId: "property-123",
  // Action text: "Xem bất động sản"
}

// English locale  
{
  type: "Contact",
  relatedPropertyId: "property-123", 
  // Action text: "View Property"
}
```

### Transaction Notification
```javascript
// Vietnamese locale
{
  type: "Transaction",
  // Action text: "Xem giao dịch"
}

// English locale
{
  type: "Transaction", 
  // Action text: "View Transaction"
}
```

## Benefits

1. **Localized Experience**: Users see action text in their preferred language
2. **Consistent Terminology**: All action text follows established translation patterns
3. **Maintainable**: Easy to update translations without code changes
4. **Extensible**: Simple to add new notification types and languages
5. **Fallback Support**: Default text for unknown notification types

## Testing Scenarios

1. **Language Switching**: Verify action text changes when locale changes
2. **Notification Types**: Test all notification types show correct action text
3. **Error Handling**: Verify error messages appear in correct language
4. **Legacy Support**: Ensure old notifications still work with translated text
5. **Fallback Behavior**: Unknown notification types show default text

## Future Enhancements

- Add more languages (Japanese, Korean, etc.)
- Context-aware action text based on user role
- Dynamic action text based on notification content
- Rich text formatting for action descriptions
