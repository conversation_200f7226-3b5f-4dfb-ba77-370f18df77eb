"use client";

import React, { useEffect, useRef, memo } from "react";
import goongjs from "@goongmaps/goong-js";
import "@goongmaps/goong-js/dist/goong-js.css";
import { HCM_COORDINATES_DISTRICT_2 } from "@/lib/enum";

const isBrowser = typeof window !== "undefined";

const SelectedLocationMap = memo(({ selectedLocation, isDisabled = false, onMarkerDragEnd }) => {
  const mapContainerRef = useRef(null);
  const mapRef = useRef(null);
  const currentMarkerRef = useRef(null);

  const center = selectedLocation || HCM_COORDINATES_DISTRICT_2;

  // Effect 1: Initialize the map instance once
  useEffect(() => {
    if (!isBrowser || !mapContainerRef.current || mapRef.current) {
      return;
    }

    goongjs.accessToken = `${process.env.NEXT_PUBLIC_GOONG_MAPTILES_KEY}`;

    const goongMap = new goongjs.Map({
      container: mapContainerRef.current,
      style: "https://tiles.goong.io/assets/goong_map_highlight.json",
      center: [center.longitude, center.latitude],
      zoom: 14,
      attributionControl: false,
      interactive: !isDisabled,
    });

    mapRef.current = goongMap;

    // Cleanup effect: Remove the map instance when component unmounts
    return () => {
      if (mapRef.current) {
        if (currentMarkerRef.current) {
          currentMarkerRef.current.remove();
          currentMarkerRef.current = null;
        }
        mapRef.current.remove();
        mapRef.current = null;
      }
    };
  }, [isDisabled]);

  // Effect 2: Update map based on selectedLocation prop and manage marker
  useEffect(() => {
    const map = mapRef.current;
    if (!map) {
      return;
    }
    // Remove old marker if exists
    if (currentMarkerRef.current) {
      currentMarkerRef.current.remove();
      currentMarkerRef.current = null;
    }

    let markerLngLat = [center.longitude, center.latitude];
    if (selectedLocation && selectedLocation?.latitude !== undefined && selectedLocation?.longitude !== undefined) {
      markerLngLat = [selectedLocation.longitude, selectedLocation.latitude];
    }

    // Create new marker
    const marker = new goongjs.Marker({
      draggable: !isDisabled,
      color: "#de5c5c",
    })
      .setLngLat(markerLngLat)
      .addTo(map);

    // Store new marker instance
    currentMarkerRef.current = marker;

    // Attach dragend event listener if not disabled
    if (!isDisabled && onMarkerDragEnd) {
      const handleDragEnd = (e) => {
        const { lng, lat } = e.target.getLngLat();
        onMarkerDragEnd({ lat, lng });
      };
      marker.on("dragend", handleDragEnd);

      // Store the listener in marker for cleanup
      marker.dragEndListener = handleDragEnd;
    }

    // Fly to selected location
    setTimeout(() => {
      map.flyTo({
        center: markerLngLat,
        essential: true,
      });
    }, 10);

    // Cleanup for this effect
    return () => {
      if (currentMarkerRef.current) {
        // Remove dragend listener if it exists
        if (currentMarkerRef.current.dragEndListener) {
          currentMarkerRef.current.off("dragend", currentMarkerRef.current.dragEndListener);
        }
        currentMarkerRef.current.remove();
        currentMarkerRef.current = null;
      }
    };
  }, [isDisabled, selectedLocation, onMarkerDragEnd]);

  // Render the map container div
  return (
    <div
      ref={mapContainerRef}
      className={`w-full h-[400px] ${isDisabled ? "opacity-75 cursor-not-allowed" : ""}`}
      style={{ position: "relative" }}
    />
  );
});

export default SelectedLocationMap;