"use server";

import { handleErrorResponse, logError } from "@/lib/apiUtils";
import { fetchWithAuth } from "@/lib/sessionUtils";

const API_BASE_URL = `${process.env.API_URL}/api/notifications`;

/**
 * Get notifications with pagination and optional type filtering
 * @param {Object} params - Query parameters
 * @param {number} params.page - Page number (default: 1)
 * @param {number} params.limit - Items per page (default: 10)
 * @param {string} params.type - Notification type
 * @returns {Promise<Object>} Response with notifications data
 */
export async function getNotifications(params = {}) {
  const { page = 1, limit = 30, type } = params;
  let url = type 
    ? `${API_BASE_URL}/category/${category}?page=${page}&pageSize=${limit}`
    : `${API_BASE_URL}?page=${page}&pageSize=${limit}`;
  return await fetchWithAuth(url, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });
}

/**
 * Get unread notification count
 * @returns {Promise<Object>} Response with notification count data
 */
export async function getUnreadCount() {
  return await fetchWithAuth(`${API_BASE_URL}/unread-count`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });
}

/**
 * Mark notifications as read
 * @param {Object} params - Request parameters
 * @param {Array<string>} params.ids - Array of notification IDs to mark as read
 * @returns {Promise<Object>} Response data
 */
export async function markAsRead(params) {
  // The API expects marking one notification at a time with a specific endpoint
  if (params.ids && params.ids.length > 0) {
    // Mark the first notification in the array
    const id = params.ids[0];
    return await fetchWithAuth(`${API_BASE_URL}/${id}/mark-as-read`, {
      method: "PUT", // Changed from POST to PUT as per API doc
      headers: {
        "Content-Type": "application/json",
      },
    });
  }
  return handleErrorResponse(false, null, "Không có ID thông báo được cung cấp");
}

/**
 * Mark all notifications as read
 * @returns {Promise<Object>} Response data
 */
export async function markAllAsRead() {
  return await fetchWithAuth(`${API_BASE_URL}/mark-all-as-read`, {
    method: "PUT", // Changed from POST to PUT as per API doc
    headers: {
      "Content-Type": "application/json",
    },
  });
}

/**
 * Get latest notifications (for navbar dropdown)
 * @param {number} limit - Number of notifications to get
 * @returns {Promise<Object>} Response with notifications data
 */
export async function getLatestNotifications(limit = 10) {
  // Using the default endpoint with a small page size for latest notifications
  return await fetchWithAuth(`${API_BASE_URL}?page=1&pageSize=${limit}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });
} 