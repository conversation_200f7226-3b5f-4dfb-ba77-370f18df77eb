import { MemberRank } from "@/lib/enum";
import { Diamond, Award, Crown, Medal, Coins, User } from "lucide-react";

/**
 * Get all member ranks with their highlight prices
 * @returns {Array} - Array of objects with rank and price
 */
export function getAllMemberRankPrices() {
  return [
    { rank: MemberRank.DIAMOND, price: "30,000₫", priceNumber: 30000 },
    { rank: MemberRank.PLATINUM, price: "35,000₫", priceNumber: 35000 },
    { rank: MemberRank.GOLD, price: "40,000₫", priceNumber: 40000 },
    { rank: MemberRank.SILVER, price: "45,000₫", priceNumber: 45000 },
    { rank: MemberRank.BRONZE, price: "50,000₫", priceNumber: 50000 },
    { rank: MemberRank.DEFAULT, price: "55,000₫", priceNumber: 55000 },
  ];
}

/**
 * Get the color class for a member rank
 * @param {string} memberRank - The member rank from MemberRank enum
 * @returns {string} - The CSS class for the color
 */
export function getMemberRankColor(memberRank) {
  const colors = {
    [MemberRank.DIAMOND]: "text-blue-500",
    [MemberRank.PLATINUM]: "text-slate-400",
    [MemberRank.GOLD]: "text-amber-500",
    [MemberRank.SILVER]: "text-gray-400",
    [MemberRank.BRONZE]: "text-orange-600",
    [MemberRank.DEFAULT]: "text-gray-600",
  };

  return colors[memberRank] || colors[MemberRank.DEFAULT];
}

/**
 * Get the translation key for a member rank
 * @param {string} memberRank - The member rank from MemberRank enum
 * @returns {string} - The translation key in Common namespace
 */
export function getMemberRankTranslationKey(memberRank) {
  const keys = {
    [MemberRank.DIAMOND]: "diamond",
    [MemberRank.PLATINUM]: "platinum",
    [MemberRank.GOLD]: "gold",
    [MemberRank.SILVER]: "silver",
    [MemberRank.BRONZE]: "bronze",
    [MemberRank.DEFAULT]: "default",
  };

  return keys[memberRank] || keys[MemberRank.DEFAULT];
}

/**
 * Get the translation key for a member rank requirement
 * @param {string} memberRank - The member rank from MemberRank enum
 * @returns {string} - The translation key for requirement in Common namespace
 */
export function getMemberRankRequirementKey(memberRank) {
  const keys = {
    [MemberRank.DIAMOND]: "requirementDiamond",
    [MemberRank.PLATINUM]: "requirementPlatinum",
    [MemberRank.GOLD]: "requirementGold",
    [MemberRank.SILVER]: "requirementSilver",
    [MemberRank.BRONZE]: "requirementBronze",
    [MemberRank.DEFAULT]: "requirementNormal",
  };

  return keys[memberRank] || keys[MemberRank.DEFAULT];
}

/**
 * Get the icon component for a member rank
 * @param {string} memberRank - The member rank from MemberRank enum
 * @returns {JSX.Element} - The icon component
 */
export function getMemberRankIcon(memberRank) {
  const icons = {
    [MemberRank.DIAMOND]: Diamond,
    [MemberRank.PLATINUM]: Award,
    [MemberRank.GOLD]: Crown,
    [MemberRank.SILVER]: Medal,
    [MemberRank.BRONZE]: Coins,
    [MemberRank.DEFAULT]: User,
  };

  const IconComponent = icons[memberRank] || icons[MemberRank.DEFAULT];
  return <IconComponent className="h-4 w-4" />;
}

/**
 * Get the spending threshold for each member rank
 * @returns {Array} - Array of objects with rank and spending threshold
 */
export function getMemberRankThresholds() {
  return [
    { rank: MemberRank.DIAMOND, condition: "> 300,000,000 trở lên" },
    { rank: MemberRank.PLATINUM, condition: "Từ 100,000,000 đến dưới 300,000,000" },
    { rank: MemberRank.GOLD, condition: "Từ 50,000,000 đến dưới 100,000,000" },
    { rank: MemberRank.SILVER, condition: "Từ 20,000,000 đến dưới 50,000,000" },
    { rank: MemberRank.BRONZE, condition: "Từ 10,000,000 đến dưới 20,000,000" },
    { rank: MemberRank.DEFAULT, condition: "< 10,000,000" },
  ];
}
