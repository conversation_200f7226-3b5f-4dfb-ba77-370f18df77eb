import { Info, Shield, Zap, Coins } from "lucide-react";
import {Link} from '@/i18n/navigation';;
import { getTranslations } from "next-intl/server";
import { formatCurrency } from "@/lib/utils";
import { MemberRank } from "@/lib/enum";
import { getAllMemberRankPrices, getMemberRankColor, getMemberRankIcon, getMemberRankTranslationKey, getMemberRankRequirementKey } from "@/lib/memberRankUtils";

// Map member ranks to background colors
const rankBgColorMap = {
  [MemberRank.DIAMOND]: "bg-blue-100",
  [MemberRank.PLATINUM]: "bg-slate-100",
  [MemberRank.GOLD]: "bg-amber-100",
  [MemberRank.SILVER]: "bg-gray-100",
  [MemberRank.BRONZE]: "bg-orange-100",
  [MemberRank.DEFAULT]: "bg-gray-100"
};

const PricePlan = async () => {
  const tPricing = await getTranslations('PricingPage');
  const tCommon = await getTranslations('Common');

  return (
    <div className="min-h-screen bg-gray-100 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-center text-gray-900 mb-8">
          {tPricing('pageTitle')}
        </h1>

        <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-8">
          <div className="flex">
            <div className="shrink-0">
              <Info className="h-5 w-5 text-yellow-500" />
            </div>
            <div className="ml-3">
              <p className="text-sm">{tPricing('vatNotice')}</p>
            </div>
          </div>
        </div>

        {/* Individual Customer Services */}
        <div className="bg-white shadow-lg rounded-lg overflow-hidden mb-8">
          <div className="px-6 py-4 bg-blue-50 border-b border-blue-100">
            <h2 className="text-xl font-semibold text-gray-900">{tPricing('individualSectionTitle')}</h2>
          </div>
          <div className="p-6">
            <table className="w-full">
              <thead>
                <tr className="bg-gray-50">
                  <th className="text-left py-3 px-4 font-semibold text-sm">{tPricing('serviceHeader')}</th>
                  <th className="text-left py-3 px-4 font-semibold text-sm">{tPricing('priceHeader')}</th>
                  <th className="text-left py-3 px-4 font-semibold text-sm">{tPricing('durationHeader')}</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-t border-gray-200">
                  <td className="py-3 px-4 text-sm font-medium">{tPricing('postingFeeService')}</td>
                  <td className="py-3 px-4 text-sm font-semibold text-blue-600">{formatCurrency(55000)}</td>
                  <td className="py-3 px-4 text-sm">{tPricing('postingFeeDuration')}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* Member Benefits */}
        <div className="bg-white shadow-lg rounded-lg overflow-hidden mb-8">
          <div className="px-6 py-4 bg-green-50 border-b border-green-100">
            <h2 className="text-xl font-semibold text-gray-900">{tPricing('benefitsSectionTitle')}</h2>
            <p className="text-sm text-gray-600 mt-1">{tPricing('benefitsSectionDesc')}</p>
          </div>
          <div className="p-6">
            <ul className="space-y-4">
              <li className="flex items-start">
                <div className="bg-blue-100 text-blue-800 px-2 py-0.5 rounded text-xs flex items-center mr-3 mt-0.5">
                  <Shield className="h-3.5 w-3.5 mr-1" />
                   {tPricing('benefit1Title')}
                </div>
                <span className="text-sm">{tPricing('benefit1Desc')}</span>
              </li>
              <li className="flex items-start">
                <div className="bg-amber-100 text-amber-800 px-2 py-0.5 rounded text-xs flex items-center mr-3 mt-0.5">
                  <Zap className="h-3.5 w-3.5 mr-1" />
                   {tPricing('benefit2Title')}
                </div>
                <span className="text-sm">{tPricing('benefit2Desc')}</span>
              </li>
              <li className="flex items-start">
                <div className="bg-green-100 text-green-800 px-2 py-0.5 rounded text-xs flex items-center mr-3 mt-0.5">
                  <Coins className="h-3.5 w-3.5 mr-1" />
                  {tPricing('benefit3Title')}
                </div>
                <span className="text-sm">{tPricing('benefit3Desc')}</span>
              </li>
            </ul>
          </div>
        </div>

        {/* Member Ranking */}
        <div className="bg-white shadow-lg rounded-lg overflow-hidden mb-8">
          <div className="px-6 py-4 bg-purple-50 border-b border-purple-100">
            <h2 className="text-xl font-semibold text-gray-900">{tPricing('rankingSectionTitle')}</h2>
            <p className="text-sm text-gray-600 mt-1">{tPricing('rankingSectionDesc')}</p>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {getAllMemberRankPrices().map(rankData => (
                  <div key={rankData.rank} className="flex items-center p-4 rounded-lg border border-gray-200 hover:bg-gray-50">
                      <div className={`${rankBgColorMap[rankData.rank]} p-2.5 rounded-full mr-4`}>
                          <span className={`${getMemberRankColor(rankData.rank)} text-lg`}>
                            {getMemberRankIcon(rankData.rank)}
                          </span>
                      </div>
                      <div>
                          <h4 className={`font-semibold ${getMemberRankColor(rankData.rank)}`}>{tCommon(getMemberRankTranslationKey(rankData.rank))}</h4>
                          <p className="text-sm text-gray-600">{tCommon(getMemberRankRequirementKey(rankData.rank))}</p>
                      </div>
                  </div>
              ))}
            </div>
          </div>
        </div>

        {/* Highlight Checkpoint Fee */}
        <div className="bg-white shadow-lg rounded-lg overflow-hidden mb-8">
          <div className="px-6 py-4 bg-amber-50 border-b border-amber-100">
            <h2 className="text-xl font-semibold text-gray-900">{tPricing('highlightFeeSectionTitle')}</h2>
            <p className="text-sm text-gray-600 mt-1">{tPricing('highlightFeeSectionDesc')}</p>
          </div>
          <div className="p-6">
            <table className="w-full">
              <thead>
                <tr className="bg-gray-50">
                  <th className="text-left py-3 px-4 font-semibold text-sm">{tPricing('rankHeader')}</th>
                  <th className="text-left py-3 px-4 font-semibold text-sm">{tPricing('priceHeader')}</th>
                </tr>
              </thead>
              <tbody>
                 {getAllMemberRankPrices().map(rankData => (
                     <tr key={rankData.rank} className="border-t border-gray-200">
                         <td className="py-3 px-4">
                             <div className="flex items-center">
                                 <span className={`${getMemberRankColor(rankData.rank)} mr-2`}>
                                   {getMemberRankIcon(rankData.rank)}
                                 </span>
                                 <span className={`${getMemberRankColor(rankData.rank)} font-medium`}>
                                   {tCommon(getMemberRankTranslationKey(rankData.rank))}
                                 </span>
                             </div>
                         </td>
                         <td className="py-3 px-4 font-semibold">{formatCurrency(rankData.priceNumber)}</td>
                     </tr>
                 ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Back to Dashboard Button */}
        <div className="text-center mt-8">
          <Link href="/user/dashboard" className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-xs text-white bg-blue-500 hover:bg-blue-600 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            {tPricing('returnButton')}
          </Link>
        </div>
      </div>
    </div>
  );
};

export default PricePlan;
