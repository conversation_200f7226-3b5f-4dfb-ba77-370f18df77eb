"use client";

import { useTranslations } from 'next-intl';
import { Link } from '@/i18n/navigation';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Wallet, InfoIcon } from "lucide-react";
import BadgeUserRank from "@/components/layout/BadgeUserRank";
import { formatCurrency } from "@/lib/utils";
import { useAuth } from "@/contexts/AuthContext";

export default function WalletInformation() {
  const t = useTranslations('UserWalletPage');
  const { profile } = useAuth();
  const walletInfo = profile?.user?.wallet;
  
  return (
    <Card className="lg:col-span-1">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-medium flex items-center">
          <Wallet className="mr-2 h-5 w-5" />
          {t('walletInfoTitle')}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex flex-col">
            <span className="text-sm text-gray-500">{t('currentBalanceLabel')}</span>
            <span className="text-2xl font-bold text-navy-blue">
              {walletInfo ? formatCurrency(walletInfo.balance) : "0₫"}
            </span>
          </div>

          <div className="flex flex-col gap-2 bg-gray-50 p-2 rounded-md">
            <span className="text-sm text-gray-500">{t('memberRankLabel')}</span>
            <BadgeUserRank
              showRefreshButton={true}
            />
          </div>

          <Separator />

          <div className="text-sm">
            <div className="mb-2 flex justify-between">
              <span className="text-gray-500">• {t('postingFeeLabel')}:</span>
              <span className="font-medium">{t('postingFeeValue')}</span>
            </div>

            <div className="mb-2 flex justify-between">
              <span className="text-gray-500">• {t('highlightFeeLabel')}:</span>
              <span className="font-medium">
                {formatCurrency(profile?.highlightFee?.fee || 0)}
              </span>
            </div>

            <div className="flex items-center mt-4">
              <InfoIcon className="h-4 w-4 mr-1.5 text-gray-400" />
              <Link href="/bieu-phi" target="_blank" className="text-xs text-blue-600 hover:underline">
                {t('viewPricingLink')}
              </Link>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
