name: 🚀 Production - Build & Deploy Frontend

on:
  workflow_dispatch:
    inputs:
      confirm_production_deploy:
        description: 'Type "DEPLOY" to confirm production deployment'
        required: true
        default: ''
      image_tag:
        description: 'Image tag to deploy (leave empty for latest commit)'
        required: false
        default: ''

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: yezhome-fe-production

jobs:
  validate-input:
    runs-on: ubuntu-latest
    steps:
      - name: Validate deployment confirmation
        run: |
          if [ "${{ github.event.inputs.confirm_production_deploy }}" != "DEPLOY" ]; then
            echo "❌ Production deployment not confirmed. Please type 'DEPLOY' to proceed."
            exit 1
          fi
          echo "✅ Production deployment confirmed"

  build-and-deploy:
    needs: validate-input
    runs-on: ubuntu-latest
    environment: production
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout source code
        uses: actions/checkout@v4

      - name: Set deployment variables
        id: vars
        run: |
          echo "repo_owner_lower=${GITHUB_REPOSITORY_OWNER,,}" >> $GITHUB_ENV
          echo "timestamp=$(date +%s)" >> $GITHUB_ENV
          
          # Use provided tag or current commit
          if [ -n "${{ github.event.inputs.image_tag }}" ]; then
            echo "deploy_tag=${{ github.event.inputs.image_tag }}" >> $GITHUB_ENV
          else
            echo "deploy_tag=${{ github.sha }}" >> $GITHUB_ENV
          fi

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build Docker image with Nginx + SSL
        run: |
          docker build \
            -f Dockerfile.nginx \
            -t ${{ env.REGISTRY }}/${{ env.repo_owner_lower }}/${{ env.IMAGE_NAME }}:latest \
            -t ${{ env.REGISTRY }}/${{ env.repo_owner_lower }}/${{ env.IMAGE_NAME }}:${{ env.deploy_tag }} \
            -t ${{ env.REGISTRY }}/${{ env.repo_owner_lower }}/${{ env.IMAGE_NAME }}:prod-${{ env.timestamp }} \
            .

      - name: Push Docker image
        run: |
          docker push ${{ env.REGISTRY }}/${{ env.repo_owner_lower }}/${{ env.IMAGE_NAME }}:latest
          docker push ${{ env.REGISTRY }}/${{ env.repo_owner_lower }}/${{ env.IMAGE_NAME }}:${{ env.deploy_tag }}
          docker push ${{ env.REGISTRY }}/${{ env.repo_owner_lower }}/${{ env.IMAGE_NAME }}:prod-${{ env.timestamp }}

      - name: Create backup of current production
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.DROPLET_IP }}
          username: ${{ secrets.DROPLET_USERNAME }}
          key: ${{ secrets.DROPLET_SSH_KEY }}
          timeout: 300s
          command_timeout: 10m
          script: |
            # Create backup of current running container
            CONTAINER_NAME="yezhome-frontend-production"
            BACKUP_NAME="yezhome-fe-backup-$(date +%Y%m%d-%H%M%S)"
            
            if docker ps | grep -q ${CONTAINER_NAME}; then
              # Get current image
              CURRENT_IMAGE=$(docker inspect ${CONTAINER_NAME} --format='{{.Config.Image}}')
              echo "Creating backup: ${BACKUP_NAME} from ${CURRENT_IMAGE}"
              
              # Tag current image as backup
              docker tag ${CURRENT_IMAGE} ${CURRENT_IMAGE}-backup-$(date +%Y%m%d-%H%M%S)
              
              echo "Backup created successfully"
            else
              echo "No running container found to backup"
            fi

      - name: Deploy to Production Droplet with SSL
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.DROPLET_IP }}
          username: ${{ secrets.DROPLET_USERNAME }}
          key: ${{ secrets.DROPLET_SSH_KEY }}
          timeout: 600s
          command_timeout: 15m
          script: |
            echo "🚀 Deploying Frontend to Production environment with SSL..."
            
            # Set environment variables
            export DEPLOY_PATH="/opt/yezhome/yezhome-frontend-production"
            export IMAGE_TAG="${{ env.deploy_tag }}"
            
            # Display deployment context
            echo "📁 Deploy path: ${DEPLOY_PATH}"
            echo "🐳 Image tag: ${IMAGE_TAG}"
            echo "🌐 Domain: yezhome.vn"
            echo "👤 Deployment type: Manual (triggered by ${{ github.actor }})"
            echo "💭 Reason: ${{ github.event.inputs.confirm_production_deploy }}"
            echo "📝 Commit: ${{ env.deploy_tag }}"
            echo "🏗️  Build number: ${{ github.run_number }}"
            
            # Create deployment directory and copy files
            mkdir -p ${DEPLOY_PATH}
            
            # Create docker-compose override for this deployment
            cat > ${DEPLOY_PATH}/docker-compose.yml << 'EOF'
            services:
              yezhome-frontend-production:
                image: ${{ env.REGISTRY }}/${{ env.repo_owner_lower }}/${{ env.IMAGE_NAME }}:${{ env.deploy_tag }}
                container_name: yezhome-frontend-production
                restart: always
                ports:
                  - "80:80"
                  - "443:443"
                environment:
                  # SSL Configuration
                  - DOMAIN=yezhome.vn
                  - CERTBOT_EMAIL=${{ secrets.CERTBOT_EMAIL }}
                  - CERTBOT_STAGING=false
                  
                  # Node.js Configuration
                  - NODE_ENV=production
                  - NODE_TLS_REJECT_UNAUTHORIZED=${{ vars.NODE_TLS_REJECT_UNAUTHORIZED || '0' }}
                  
                  # API Configuration
                  - API_URL=${{ vars.API_URL || 'https://api.yezhome.vn' }}
                  - JWT_SECRET=${{ secrets.JWT_SECRET }}
                  
                  # UploadThing Configuration
                  - UPLOADTHING_TOKEN=${{ secrets.UPLOADTHING_TOKEN }}
                  
                  # Goong Maps Configuration (Server-side)
                  - GOONG_MAPTILES_KEY=${{ secrets.GOONG_MAPTILES_KEY }}
                  - GOONG_GEO_API_KEY=${{ secrets.GOONG_GEO_API_KEY }}
                  
                  # Next.js Public Variables (exposed to browser)
                  - NEXT_PUBLIC_API_URL=${{ vars.NEXT_PUBLIC_API_URL || 'https://api.yezhome.vn' }}
                  - NEXT_PUBLIC_INTERNAL_API_URL=${{ vars.NEXT_PUBLIC_INTERNAL_API_URL || 'https://adapi.yezhome.vn' }}
                  - NEXT_PUBLIC_FRONTEND_URL=${{ vars.NEXT_PUBLIC_FRONTEND_URL || 'https://yezhome.vn' }}
                  - NEXT_PUBLIC_GOONG_MAPTILES_KEY=${{ secrets.NEXT_PUBLIC_GOONG_MAPTILES_KEY }}
                  - NEXT_PUBLIC_GOONG_GEO_API_KEY=${{ secrets.NEXT_PUBLIC_GOONG_GEO_API_KEY }}
                  - NEXT_PUBLIC_ANALYTICS_ID=${{ vars.NEXT_PUBLIC_ANALYTICS_ID }}
                  
                  # Firebase Configuration (Public)
                  - NEXT_PUBLIC_FIREBASE_API_KEY=${{ secrets.NEXT_PUBLIC_FIREBASE_API_KEY }}
                  - NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=${{ vars.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN }}
                  - NEXT_PUBLIC_FIREBASE_PROJECT_ID=${{ vars.NEXT_PUBLIC_FIREBASE_PROJECT_ID }}
                  - NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=${{ vars.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET }}
                  - NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=${{ vars.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID }}
                  - NEXT_PUBLIC_FIREBASE_APP_ID=${{ vars.NEXT_PUBLIC_FIREBASE_APP_ID }}
                  - NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=${{ vars.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID }}
                  
                  # Deployment Metadata
                  - IMAGE_TAG=${{ env.deploy_tag }}
                  - BUILD_NUMBER=${{ github.run_number }}
                  - COMMIT_SHA=${{ env.deploy_tag }}
                  - LAST_DEPLOYED=$(date -u '+%Y-%m-%d %H:%M:%S UTC')
                  - DEPLOYED_BY=${{ github.actor }}
                volumes:
                  - frontend_ssl_certs_prod:/etc/letsencrypt
                  - frontend_ssl_challenges_prod:/var/www/certbot
                networks:
                  - yezhome-frontend-prod-network
                labels:
                  - "environment=production"
                  - "domain=yezhome.vn"
                healthcheck:
                  test: ["CMD", "curl", "-f", "http://localhost/health"]
                  interval: 30s
                  timeout: 10s
                  retries: 3
                  start_period: 40s
            
            volumes:
              frontend_ssl_certs_prod:
                name: yezhome_prod_frontend_ssl_certs
              frontend_ssl_challenges_prod:
                name: yezhome_prod_frontend_ssl_challenges
            
            networks:
              yezhome-frontend-prod-network:
                name: yezhome-frontend-prod-network
                driver: bridge
            EOF
            
            # Login to GitHub Container Registry
            echo "🔐 Logging into GitHub Container Registry..."
            echo "${{ secrets.PULL_PAT }}" | docker login ${{ env.REGISTRY }} -u ${{ github.actor }} --password-stdin
            
            # Navigate to deployment directory
            cd ${DEPLOY_PATH}
            
            # Pull the latest image
            echo "📥 Pulling image: ${{ env.REGISTRY }}/${{ env.repo_owner_lower }}/${{ env.IMAGE_NAME }}:${{ env.deploy_tag }}"
            docker pull ${{ env.REGISTRY }}/${{ env.repo_owner_lower }}/${{ env.IMAGE_NAME }}:${{ env.deploy_tag }}
            
            # Stop existing services
            echo "🛑 Stopping existing services..."
            docker compose down || true
            
            # Start services with SSL
            echo "🚀 Starting services with SSL..."
            docker compose up -d
            
            # Wait for services to be ready
            echo "⏳ Waiting for services to be ready (SSL generation may take time)..."
            sleep 60
            
            # Check SSL certificate generation
            echo "🔍 Checking SSL certificates for production..."
            timeout 300 bash -c "until curl -sSf https://yezhome.vn/health >/dev/null 2>&1; do sleep 10; done" && echo "✅ yezhome.vn SSL working" || echo "⚠️ SSL may still be setting up"
            
            # Verify container is running
            if ! docker ps | grep -q yezhome-frontend-production; then
              echo "❌ Deployment failed - container not running"
              echo "Container logs:"
              docker logs yezhome-frontend-production || true
              exit 1
            fi
            
            # Logout from registry
            echo "🔐 Logging out of GitHub Container Registry..."
            docker logout ${{ env.REGISTRY }}
            
            echo "✅ Production deployment with SSL completed successfully!"
            echo ""
            echo "📊 Deployment Summary:"
            echo "  Environment: Production"
            echo "  Image Tag: ${IMAGE_TAG}"
            echo "  Build: #${{ github.run_number }}"
            echo "  Commit: ${{ env.deploy_tag }}"
            echo "  Deployed by: ${{ github.actor }}"
            echo "  Frontend URL: https://yezhome.vn"
            echo "  Completed: $(date -u '+%Y-%m-%d %H:%M:%S UTC')"

      - name: Cleanup old images
        if: success()
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.DROPLET_IP }}
          username: ${{ secrets.DROPLET_USERNAME }}
          key: ${{ secrets.DROPLET_SSH_KEY }}
          timeout: 300s
          command_timeout: 10m
          script: |
            # Clean up old production images (keep last 5)
            echo "Cleaning up old images..."
            docker images ${{ env.REGISTRY }}/${{ env.repo_owner_lower }}/${{ env.IMAGE_NAME }} --format "table {{.Tag}}\t{{.CreatedAt}}" | tail -n +6 | awk '{print $1}' | grep -v "backup" | xargs -r -I {} docker rmi ${{ env.REGISTRY }}/${{ env.repo_owner_lower }}/${{ env.IMAGE_NAME }}:{} || true
            
            # Clean up dangling images
            docker image prune -f || true
            
            echo "Cleanup completed"

      - name: Rollback on Failure
        if: failure()
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.DROPLET_IP }}
          username: ${{ secrets.DROPLET_USERNAME }}
          key: ${{ secrets.DROPLET_SSH_KEY }}
          timeout: 300s
          command_timeout: 10m
          script: |
            echo "🔄 Attempting rollback..."
            
            # Find the most recent backup
            BACKUP_IMAGE=$(docker images --format "table {{.Repository}}:{{.Tag}}" | grep "backup" | head -1)
            
            if [ -n "$BACKUP_IMAGE" ]; then
              echo "Rolling back to: $BACKUP_IMAGE"
              
              # Stop failed services
              cd /opt/yezhome/yezhome-frontend-production
              docker compose down || true
              
              # Update compose file with backup image
              sed -i "s|image: .*|image: $BACKUP_IMAGE|" docker-compose.yml
              
              # Start backup services
              docker compose up -d
              
              echo "✅ Rollback completed"
            else
              echo "❌ No backup found for rollback"
            fi

      - name: Deployment Summary
        if: always()
        run: |
          if [ "${{ job.status }}" == "success" ]; then
            echo "🎉 Production deployment with SSL successful!"
            echo "🌐 Frontend URL: https://yezhome.vn"
            echo "📦 Deployed Image: ${{ env.REGISTRY }}/${{ env.repo_owner_lower }}/${{ env.IMAGE_NAME }}:${{ env.deploy_tag }}"
            echo "⏰ Deployment Time: $(date -u)"
          else
            echo "💥 Production deployment failed!"
            echo "🔄 Check rollback status above"
          fi
