"use server";

import { fetchWithAuth } from "@/lib/sessionUtils";

const API_USER_BASE_URL = `${process.env.API_URL}/api/user`;
const API_WALLET_BASE_URL = `${process.env.API_URL}/api/Wallet`;

export async function getUserWallet() {
  return await fetchWithAuth(`${API_WALLET_BASE_URL}/balance`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });
}

export async function getUserTransactions(count = 30) {
  return await fetchWithAuth(`${API_USER_BASE_URL}/transactions?pageSize=${count}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });
}

export async function getTransactionDetail(transactionId) {
  return await fetchWithAuth(`${API_USER_BASE_URL}/transactions/${transactionId}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });
}

export async function createTopUpRequest(amount, paymentMethod) {
  // Create a pending transaction
  const createTopUpResponse = await fetchWithAuth(`${API_WALLET_BASE_URL}/topup`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      amount,
      paymentMethod,
    }),
  });

  return createTopUpResponse;
}

// Verify bank transfer
export async function verifyBankTransfer(orderId, transactionInfo) {
  return await fetchWithAuth(`${API_WALLET_BASE_URL}/${orderId}/verify-transfer`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(transactionInfo),
  });
}

// Check payment status
export async function checkPaymentStatus(refId) {
  return await fetchWithAuth(`${API_WALLET_BASE_URL}/status/${refId}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });
}

// Get payment methods and settings
export async function getPaymentSettings() {
  return await fetchWithAuth(`${process.env.API_URL}/api/user/payment-settings`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });
} 