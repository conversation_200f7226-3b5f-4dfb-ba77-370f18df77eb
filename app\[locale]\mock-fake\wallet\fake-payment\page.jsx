"use client"

import { useRouter, useSearchParams } from 'next/navigation';
import { useState } from 'react';

export default function FakePaymentPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const transactionReference = searchParams.get('transactionReference');
  const amount = searchParams.get('amount');
  const [isLoading, setIsLoading] = useState(false);
    
  const handleProcessPayment = async (isSuccess) => {
    setIsLoading(true);
    try {
      // Gọi đến một endpoint "test" đặc biệt trên backend để kích hoạt kết quả
      const response = await fetch('https://localhost:7209/api/FakePayment/payment-notification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transactionReference: transactionReference,
          isSuccess: isSuccess
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // <PERSON>u khi kích hoạt <PERSON>ong, chuy<PERSON>n hướng về trang kết quả thật
      const status = isSuccess ? 'success' : 'failed';
      router.push(`/user/wallet/result?status=${status}&refId=${transactionReference}`);

    } catch (error) {
      console.error('Failed to process fake payment', error);
      alert('Đã có lỗi xảy ra khi giả lập thanh toán.');
      setIsLoading(false);
    }
  };

  if (!transactionReference) {
    return <div>Đang tải thông tin giao dịch...</div>;
  }

  return (
    <div style={{ padding: '2rem', fontFamily: 'sans-serif' }}>
      <h1>Trang Giả Lập Thanh Toán</h1>
      <p>Đây là trang giả lập để kiểm thử luồng thanh toán.</p>
      <hr />
      <p><strong>Mã Giao dịch:</strong> {transactionReference}</p>
      <p><strong>Số tiền:</strong> {Number(amount).toLocaleString('vi-VN')} VND</p>
      <hr />
      <p>Vui lòng chọn kết quả bạn muốn giả lập:</p>
      <div style={{ display: 'flex', gap: '1rem', marginTop: '1rem' }}>
        <button onClick={() => handleProcessPayment(true)} disabled={isLoading}>
          {isLoading ? 'Đang xử lý...' : '✅ Giả lập Thành công'}
        </button>
        <button onClick={() => handleProcessPayment(false)} disabled={isLoading} style={{ background: '#ef4444' }}>
          {isLoading ? 'Đang xử lý...' : '❌ Giả lập Thất bại'}
        </button>
      </div>
    </div>
  );
}