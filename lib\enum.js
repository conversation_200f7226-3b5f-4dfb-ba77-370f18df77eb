import BankingIcon from "@/app/icon/banking-icon";
import MomoIcon from "@/app/icon/momo-icon";
import VnPayIcon from "@/app/icon/vnpay-icon";

export const FormType = Object.freeze({
  EDIT: "Edit",
  NEW: "New",
});

// PropertyStatus Enum
export const PropertyStatus = Object.freeze({
  DRAFT: "Draft",
  PENDING_APPROVAL: "PendingApproval",
  APPROVED: "Approved",
  REJECTED_BY_ADMIN: "RejectedByAdmin",
  REJECTED_DUE_TO_UNPAID: "RejectedDueToUnpaid",
  WAITING_PAYMENT: "WaitingPayment",
  EXPIRED: "Expired",
  SOLD: "Sold",
});

// PropertyType Enum
export const PropertyType = Object.freeze({
  APARTMENT: "can_ho",
  HOUSE: "nha_pho",
  MOTEL: "nha_tro",
});

// PostType Enum to match C# PostType
export const PostType = Object.freeze({
  SALE: "Sale",
  RENT: "Rent",
});

export const DEFAULT_ITEM_PER_PAGE = 30;
export const DEFAULT_PAGE = 1;
export const DEFAULT_POST_PRICE = 55000;

// Enum for member ranks
export const MemberRank = Object.freeze({
  DIAMOND: "diamond",
  PLATINUM: "platinum",
  GOLD: "gold",
  SILVER: "silver",
  BRONZE: "bronze",
  DEFAULT: "default",
});

// Highlight prices based on member rank
export const highlightPrices = Object.freeze({
  [MemberRank.DIAMOND]: 30000,
  [MemberRank.PLATINUM]: 35000,
  [MemberRank.GOLD]: 40000,
  [MemberRank.SILVER]: 45000,
  [MemberRank.BRONZE]: 50000,
  [MemberRank.DEFAULT]: 55000,
});

// Default coordinates for Ho Chi Minh City
export const HCM_COORDINATES_DISTRICT_2 = Object.freeze({
  latitude: 10.79, // Tọa độ ví dụ cho Quận 2 (nay thuộc TP Thủ Đức)
  longitude: 106.73,
  accuracy: 0, // Vị trí mặc định không có dữ liệu độ chính xác
});

export const CAN_NOT_EDIT_STATUS = [
  PropertyStatus.PENDING_APPROVAL,
  PropertyStatus.APPROVED,
  PropertyStatus.SOLD,
  PropertyStatus.EXPIRED,
  PropertyStatus.WAITING_PAYMENT,
];

export const CAN_NOT_SEND_TO_REVIEW_STATUS = [
  PropertyStatus.PENDING_APPROVAL, 
  PropertyStatus.APPROVED, 
  PropertyStatus.SOLD, 
  PropertyStatus.EXPIRED];

export const USER_TYPE = Object.freeze({
  SELLER: "Seller",
  BUYER: "Buyer",
  ADMIN: "Admin",
});

// Notification Category Enum (replaces old NOTIFICATION_TYPE)
export const NOTIFICATION_CATEGORY = Object.freeze({
  LISTING: "Listing",        // Tin đăng
  FINANCE: "Finance",        // Tài chính
  PROMOTION: "Promotion",    // Khuyến mãi
  ACCOUNT: "Account",        // Tài khoản
  MISCELLANEOUS: "Miscellaneous"  // Khác
});

// Notification Type Enum (new detailed types)
export const NOTIFICATION_TYPE = Object.freeze({
  // Category: Listing (Tin đăng)
  LISTING_CREATED: "ListingCreated",          // Tin đăng được tạo
  LISTING_APPROVED: "ListingApproved",          // Tin đăng được duyệt
  LISTING_REJECTED: "ListingRejected",          // Tin đăng bị từ chối
  LISTING_EXPIRED: "ListingExpired",            // Tin đăng sắp hết hạn / đã hết hạn
  NEW_CONTACT_REQUEST: "NewContactRequest",     // Có yêu cầu liên hệ mới cho tin đăng

  // Category: Finance (Tài chính)
  WALLET_TOP_UP_SUCCESS: "WalletTopUpSuccess",       // Nạp tiền vào ví thành công
  WALLET_TOP_UP_FAILED: "WalletTopUpFailed",         // Nạp tiền thất bại
  SERVICE_PAYMENT_SUCCESS: "ServicePaymentSuccess",  // Thanh toán dịch vụ thành công
  LOW_BALANCE_WARNING: "LowBalanceWarning",          // Cảnh báo số dư sắp hết

  // Category: Promotion (Khuyến mãi)
  NEW_DISCOUNT_AVAILABLE: "NewDiscountAvailable",     // Có chương trình khuyến mãi mới
  PROMOTIONAL_CODE_RECEIVED: "PromotionalCodeReceived", // Bạn nhận được một mã giảm giá

  // Category: Account (Tài khoản)
  WELCOME_USER: "WelcomeUser",              // Chào mừng thành viên mới
  PASSWORD_RESET_REQUEST: "PasswordResetRequest",     // Yêu cầu đặt lại mật khẩu
  ACCOUNT_SECURITY_ALERT: "AccountSecurityAlert",     // Cảnh báo đăng nhập lạ

  // Category: Miscellaneous (Khác)
  SYSTEM_MAINTENANCE: "SystemMaintenance",        // Thông báo bảo trì hệ thống
  FEATURE_ANNOUNCEMENT: "FeatureAnnouncement",    // Thông báo về tính năng mới
  GENERAL_SYSTEM_MESSAGE: "GeneralSystemMessage"  // Thông báo chung khác
});

export const TRANSACTION_TYPE = Object.freeze({
  DEPOSIT: "DEPOSIT",
  SPEND: "SPEND", 
  SPEND_POST: "SPEND_POST",
  SPEND_HIGHLIGHT: "SPEND_HIGHLIGHT",
});

export const TRANSACTION_STATUS = Object.freeze({
  COMPLETED: "COMPLETED",
  PENDING: "PENDING",
  FAILED: "FAILED",
  CANCELLED: "CANCELLED",
});

export const PAYMENT_METHODS = [
  {
    id: "banking",
    name: "bankingTransfer",
    icon: <BankingIcon className="h-6 w-6" />,
    description: "bankingTransferDescription",
    tranferCodePrefix: "TBK",
  },
  {
    id: "momo",
    name: "momoPay",
    icon: <MomoIcon className="h-6 w-6" />,
    description: "momoPayDescription",
    tranferCodePrefix: "TMM",
  },
  {
    id: "vnpay",
    name: "vnPay",
    icon: <VnPayIcon className="h-6 w-6" />,
    description: "vnPayDescription",
    tranferCodePrefix: "TVN",
  },
];

export const PAYMENT_STATUS = Object.freeze({
  PENDING: "PENDING",
  SUCCESS: "COMPLETED",
  FAILED: "FAILED",
  CANCELLED: "CANCELLED",
});

export const PRESET_AMOUNTS = [
  { value: 100000, label: "100,000₫" },
  { value: 200000, label: "200,000₫" },
  { value: 500000, label: "500,000₫" },
  { value: 1000000, label: "1,000,000₫" },
  { value: 2000000, label: "2,000,000₫" },
  { value: 3000000, label: "3,000,000₫" },
];

export const PropertyEventType = Object.freeze({
  CLICK_PHONE: "click_phone",
  CHAT: "chat",
  FAVORITE: "favorite",
  UNFAVORITE: "unfavorite",
  SUBMIT_CONTACT_FORM: "submit_contact_form",
  CLICK_MAP: "click_map",
  SHARE: "share",
});
