"use client";

import { useEffect, useRef } from "react";
import goongjs from "@goongmaps/goong-js";
import "@goongmaps/goong-js/dist/goong-js.css";
import { formatPriceShort } from "@/lib/utils";
import { useTranslations } from "next-intl";
import { MapPropertyPopup } from "./MapPropertyPopup";
import ReactDOM from "react-dom/client";

const isBrowser = typeof window !== "undefined";

const HomeMap = ({ markers = [], center, onBoundsChange, onViewDetails, activePropertyId = null }) => {
  const mapContainerRef = useRef(null);
  const mapRef = useRef(null);
  const markerRootsRef = useRef([]);
  const handleMoveEndRef = useRef(null);
  const currentOpenPopupRef = useRef(null);

  const tCommon = useTranslations("Common");

  // --- <PERSON> khai báo instance cho bản đồ ---
  useEffect(() => {
    if (!isBrowser || !mapContainerRef.current || mapRef.current) {
      return;
    }

    goongjs.accessToken = `${process.env.NEXT_PUBLIC_GOONG_MAPTILES_KEY}`;

    const goongMap = new goongjs.Map({
      container: mapContainerRef.current,
      style: "https://tiles.goong.io/assets/goong_map_highlight.json",
      center: [center.longitude, center.latitude],
      zoom: 15,
    });

    mapRef.current = goongMap;
    goongMap.on("load", () => {
      const currentBounds = goongMap.getBounds();
      if (onBoundsChange) {
        onBoundsChange(currentBounds);
      }
    });

    return () => {
      if (mapRef.current) {
        if (handleMoveEndRef.current) {
          mapRef.current.off("moveend", handleMoveEndRef.current);
        }
        mapRef.current.remove();
        mapRef.current = null;
      }
    };
  }, [center, onBoundsChange]);

  // --- Effect xử lý sự kiện moveend của bản đồ ---
  useEffect(() => {
    const map = mapRef.current;
    if (!map || !center || !onBoundsChange) {
      return;
    }
    const currentCenter = map.getCenter();
    const centerHasChanged = currentCenter.lat !== center.latitude || currentCenter.lng !== center.longitude;
    if (centerHasChanged) {
      map.flyTo({ center: [center.longitude, center.latitude], essential: true });
    }
    handleMoveEndRef.current = (event) => {
      const currentBounds = event.target.getBounds();
      if (onBoundsChange) {
        onBoundsChange(currentBounds);
      }
    };
    map.off("moveend", handleMoveEndRef.current);
    map.on("moveend", handleMoveEndRef.current);
    return () => {
      if (mapRef.current && handleMoveEndRef.current) {
        mapRef.current.off("moveend", handleMoveEndRef.current);
      }
    };
  }, [center, onBoundsChange, mapRef.current]);

  // --- Effect xử lý Markers ---
  useEffect(() => {
    const map = mapRef.current;
    if (!map || !markers) {
      markerRootsRef.current.forEach((item) => {
        setTimeout(() => {
          if (item.root) {
            try {
              item.root.unmount();
            } catch (e) {
              console.error(e);
            }
          }
          if (item.marker && item.marker.remove) {
            try {
              item.marker.remove();
            } catch (e) {
              console.error(e);
            }
          }
        }, 0);
      });

      markerRootsRef.current = [];
      return;
    }

    markerRootsRef.current.forEach((item) => {
      setTimeout(() => {
        if (item.root) {
          try {
            item.root.unmount();
          } catch (e) {
            console.error(e);
          }
        }
        if (item.marker && item.marker.remove) {
          try {
            item.marker.remove();
          } catch (e) {
            console.error(e);
          }
        }
      }, 0);
    });
    markerRootsRef.current = [];

    // Thêm markers mới nếu có
    if (markers && markers.length > 0) {
      markers.forEach((markerData, index) => {
        if (markerData.latitude !== undefined && markerData.longitude !== undefined) {
          try {
            // --- TẠO PHẦN TỬ DOM TÙY CHỈNH CHO MARKER ---
            const el = document.createElement("div");
            el.className = `custom-price-marker ${markerData.isHighlighted ? "highlighted" : ""}`;
            el.innerHTML = `
                 <div class="price-bubble">${formatPriceShort(markerData.price)}</div>
                 <div class="price-arrow"></div>
            `;

            var markerHeight = 50,
              markerRadius = 10,
              linearOffset = 25;
            var popupOffsets = {
              top: [0, 0],
              "top-left": [0, 0],
              "top-right": [0, 0],
              bottom: [0, -markerHeight],
              "bottom-left": [linearOffset, (markerHeight - markerRadius + linearOffset) * -1],
              "bottom-right": [-linearOffset, (markerHeight - markerRadius + linearOffset) * -1],
              left: [markerRadius, (markerHeight - markerRadius) * -1],
              right: [-markerRadius, (markerHeight - markerRadius) * -1],
            };

            const popupContainer = document.createElement("div");
            const popup = new goongjs.Popup({
              offset: popupOffsets,
              closeButton: false,
              closeOnClick: true,
            });
            const root = ReactDOM.createRoot(popupContainer);
            root.render(<MapPropertyPopup property={markerData} onViewDetails={onViewDetails} tCommon={tCommon} />);
            popup.setDOMContent(popupContainer);

            const marker = new goongjs.Marker(el, { anchor: "bottom" })
              .setLngLat([markerData.longitude, markerData.latitude])
              .setPopup(popup)
              .addTo(map);

            // --- LƯU TRỮ INSTANCE MARKER VÀ ROOT REACT ---
            markerRootsRef.current.push({ marker: marker, root: root, propertyId: markerData.id });
          } catch (markerError) {
            console.error(`GoongMap Effect Markers: Lỗi khi tạo hoặc thêm marker ${index}:`, markerData, markerError); // <-- LOG LỖI TẠO/THÊM MARKER
          }
        }
      });
    }

    // --- Cleanup cho effect markers ---
    return () => {
      markerRootsRef.current.forEach((item) => {
        setTimeout(() => {
          if (item.root) {
            try {
              item.root.unmount();
            } catch (e) {
              console.error(e);
            }
          }
          if (item.marker && item.marker.remove) {
            try {
              item.marker.remove();
            } catch (e) {
              console.error(e);
            }
          }
        }, 0);
      });
      markerRootsRef.current = [];
    };
  }, [markers, mapRef.current]); // Dependency: markers thay đổi HOẶC mapRef.current có giá trị

  // Effect to open/close popup based on activePropertyId prop
  useEffect(() => {
    const map = mapRef.current;
    if (!map) {
      // Cleanup popup if map disappears while activeId is not null
      if (currentOpenPopupRef.current) {
        currentOpenPopupRef.current.remove();
        currentOpenPopupRef.current = null;
      }
      return;
    }

    // --- Bước 1: Đóng popup hiện đang được mở bởi effect này ---
    // Sử dụng ref để theo dõi popup đã mở trước đó
    if (currentOpenPopupRef.current) {
      try {
        currentOpenPopupRef.current.remove(); // Đóng và xóa popup cũ
      } catch (e) {
        console.error("Error removing previous popup:", e);
      }
      currentOpenPopupRef.current = null; // Reset ref
    }

    if (activePropertyId === null) {
      return;
    }

    // --- Bước 2: Tìm marker mục tiêu và mở popup của nó ---
    const activeMarkerItem = markerRootsRef.current.find((item) => item.propertyId === activePropertyId);
    if (activeMarkerItem && activeMarkerItem.marker) {
      const popupInstance = activeMarkerItem.marker.getPopup();
      if (popupInstance) {
        if (!popupInstance.isOpen()) {
          popupInstance.addTo(map); // Mở popup
        }
        currentOpenPopupRef.current = popupInstance; // <--- LƯU VÀO REF
      }
    }
    return () => {
      if (currentOpenPopupRef.current) {
        try {
          currentOpenPopupRef.current.remove();
        } catch (e) {
          console.error("Error removing popup during cleanup:", e);
        }
        currentOpenPopupRef.current = null; // Reset ref
      }
    }; // Cleanup handled by closing at start of effect
  }, [activePropertyId, mapRef.current, markerRootsRef.current, markers]);

  return (
    <div className={`w-3/5 h-[calc(100vh-227px)] relative z-0 opacity-75 cursor-not-allowed`}>
      <div ref={mapContainerRef} className="w-full h-full home-map" />
    </div>
  );
};

export default HomeMap;
