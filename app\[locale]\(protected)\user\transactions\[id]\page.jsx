"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { getTransactionDetail, verifyBankTransfer } from "@/app/actions/server/wallet";
import { formatCurrency } from "@/lib/utils";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  Clock, 
  CheckCircle2, 
  XCircle, 
  AlertTriangle,
  CreditCard,
  Landmark,
  QrCode,
  Link as LinkIcon
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import {Link} from '@/i18n/navigation';;
import { toast } from "@/hooks/use-toast";
import { useTranslations } from 'next-intl';

// Payment method icons
const getPaymentMethodIcon = (method) => {
  switch (method) {
    case "banking":
      return <Landmark className="h-5 w-5" />;
    case "momo":
      return <QrCode className="h-5 w-5 text-pink-600" />;
    case "card":
      return <CreditCard className="h-5 w-5" />;
    default:
      return <CreditCard className="h-5 w-5" />;
  }
};

// Status badge component
const StatusBadge = ({ status, t }) => {
  const statusConfig = {
    completed: {
      color: "bg-green-100 text-green-800 hover:bg-green-100",
      label: t('statusCompleted'),
      icon: <CheckCircle2 className="h-3.5 w-3.5 mr-1" />
    },
    pending: {
      color: "bg-amber-100 text-amber-800 hover:bg-amber-100",
      label: t('statusPending'),
      icon: <Clock className="h-3.5 w-3.5 mr-1" />
    },
    failed: {
      color: "bg-red-100 text-red-800 hover:bg-red-100",
      label: t('statusFailed'),
      icon: <XCircle className="h-3.5 w-3.5 mr-1" />
    },
    cancelled: {
      color: "bg-gray-100 text-gray-800 hover:bg-gray-100",
      label: t('statusCancelled'),
      icon: <AlertTriangle className="h-3.5 w-3.5 mr-1" />
    }
  };
  
  const config = statusConfig[status] || statusConfig.pending;
  
  return (
    <Badge rounded="full" variant="outline" className={`${config.color} flex items-center`}>
      {config.icon}
      {config.label}
    </Badge>
  );
};

export default function TransactionDetailPage() {
  const t = useTranslations('UserTransactionsPage');
  const [transaction, setTransaction] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [verifying, setVerifying] = useState(false);
  
  const params = useParams();
  const router = useRouter();
  const transactionId = params.id;
  
  useEffect(() => {
    const fetchTransactionDetail = async () => {
      try {
        setLoading(true);
        const response = await getTransactionDetail(transactionId);
        
        if (response?.isSuccess) {
          setTransaction(response.data);
        } else {
          setError(response.message || t('transactionFetchError'));
        }
      } catch (err) {
        console.error(err);
        setError(t('transactionGenericFetchError'));
      } finally {
        setLoading(false);
      }
    };
    
    if (transactionId) {
      fetchTransactionDetail();
    }
  }, [transactionId, t]);
  
  const handleVerifyTransfer = async () => {
    try {
      setVerifying(true);
      const response = await verifyBankTransfer(transactionId);
      
      if (response?.isSuccess) {
        toast({
          title: t('verifySuccessTitle'),
          description: t('verifySuccessMessage'),
        });
        
        // Refresh transaction data
        const refreshResponse = await getTransactionDetail(transactionId);
        if (refreshresponse?.isSuccess) {
          setTransaction(refreshResponse.data);
        }
      } else {
        toast({
          variant: "destructive",
          title: t('verifyErrorTitle'),
          description: response.message || t('verifyErrorMessage'),
        });
      }
    } catch (err) {
      console.error(err);
      toast({
        variant: "destructive",
        title: t('verifyErrorTitle'),
        description: t('verifyGenericErrorMessage'),
      });
    } finally {
      setVerifying(false);
    }
  };
  
  if (loading) {
    return (
      <div className="p-6">
        <div className="h-96 flex items-center justify-center">
          <div className="text-coral-500">{t('loadingMessage')}</div>
        </div>
      </div>
    );
  }
  
  if (error || !transaction) {
    return (
      <div className="p-6">
        <div className="h-96 flex flex-col items-center justify-center">
          <div className="text-red-500 mb-4">{error || t('transactionNotFound')}</div>
          <Button variant="outline" asChild>
            <Link href="/user/transactions">{t('backToListButton')}</Link>
          </Button>
        </div>
      </div>
    );
  }
  
  // Helper for payment method name
  const getPaymentMethodName = (method) => {
      switch (method) {
        case "banking": return t('paymentMethodBank');
        case "momo": return t('paymentMethodMomo');
        case "card": return t('paymentMethodCard');
        default: return method; // Fallback
      }
  };
  
  return (
    <div className="p-6 max-w-2xl mx-auto">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold text-navy-blue">{t('detailPageTitle')}</h1>
        <Button variant="outline" size="sm" asChild>
          <Link href="/user/transactions">
            {t('backButton')}
          </Link>
        </Button>
      </div>
      
      <Card className="shadow-xs">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-center">
            <CardTitle className="text-xl">
              {transaction.type === "topup" ? t('cardTitleDeposit') : (transaction.description || t('cardTitlePayment'))}
            </CardTitle>
            <StatusBadge status={transaction.status} t={t} />
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Transaction amount */}
          <div className="flex justify-center pt-2 pb-4">
            <div className="text-center">
              <div className={`text-3xl font-bold ${transaction.type === "topup" ? "text-green-600" : "text-red-600"}`}>
                {transaction.type === "topup" ? "+" : "-"} {formatCurrency(transaction.amount)}
              </div>
              <div className="text-sm text-gray-500 mt-1">
                {new Date(transaction.createdAt).toLocaleString(router.locale || 'vi-VN')}
              </div>
            </div>
          </div>
          
          {/* Transaction details */}
          <div className="space-y-4 bg-gray-50 p-4 rounded-md">
            <div className="flex justify-between">
              <span className="text-gray-600">{t('transactionIdLabel')}</span>
              <span className="font-medium">{transaction.id}</span>
            </div>
            
            {transaction.orderId && (
              <div className="flex justify-between">
                <span className="text-gray-600">{t('orderIdLabel')}</span>
                <span className="font-medium">{transaction.orderId}</span>
              </div>
            )}
            
            <div className="flex justify-between">
              <span className="text-gray-600">{t('transactionTypeLabel')}</span>
              <span className="font-medium">
                {transaction.type === "topup" ? t('transactionTypeDeposit') : t('transactionTypePayment')}
              </span>
            </div>
            
            {transaction.paymentMethod && (
              <div className="flex justify-between items-center">
                <span className="text-gray-600">{t('paymentMethodLabel')}</span>
                <div className="flex items-center">
                  {getPaymentMethodIcon(transaction.paymentMethod)}
                  <span className="font-medium ml-1.5">
                    {getPaymentMethodName(transaction.paymentMethod)}
                  </span>
                </div>
              </div>
            )}
            
            <div className="flex justify-between">
              <span className="text-gray-600">{t('creationDateLabel')}</span>
              <span className="font-medium">
                {new Date(transaction.createdAt).toLocaleString(router.locale || 'vi-VN')}
              </span>
            </div>
            
            {transaction.updatedAt && transaction.status === "completed" && (
              <div className="flex justify-between">
                <span className="text-gray-600">{t('completionDateLabel')}</span>
                <span className="font-medium">
                  {new Date(transaction.updatedAt).toLocaleString(router.locale || 'vi-VN')}
                </span>
              </div>
            )}
            
            {transaction.description && (
              <div className="flex justify-between pt-2 border-t mt-4">
                <span className="text-gray-600 font-medium">{t('descriptionLabel')}</span>
                <span className="text-right">{transaction.description}</span>
              </div>
            )}
            {transaction.relatedPropertyId && (
                <div className="flex justify-end pt-2 border-t mt-4">
                    <Button variant="link" size="sm" asChild className="text-blue-600">
                        <Link href={`/user/bds/${transaction.relatedPropertyId}`}>
                           <LinkIcon className="h-4 w-4 mr-1" /> {t('relatedListingLink')} 
                        </Link>
                    </Button>
                </div>
            )}
          </div>
          
          {/* Verify Button for pending bank transfers */}
          {transaction.paymentMethod === "banking" && transaction.status === "pending" && (
            <div className="mt-6 text-center">
              <Button 
                onClick={handleVerifyTransfer} 
                disabled={verifying}
                className="w-full md:w-auto bg-teal-600 hover:bg-teal-700"
              >
                {verifying ? t('verifyingButton') : t('verifyButton')}
              </Button>
              <p className="text-xs text-gray-500 mt-2">Chỉ nhấn nút này sau khi bạn đã thực hiện chuyển khoản.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 