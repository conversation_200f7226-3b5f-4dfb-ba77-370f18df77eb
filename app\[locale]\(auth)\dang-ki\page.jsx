import { Link } from "@/i18n/navigation";
import RegisterForm from "./RegisterForm";
import { getTranslations } from "next-intl/server";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import Image from "next/image";

const RegisterPage = async () => {
  // Use LoginPage namespace for logoAlt for consistency
  const t = await getTranslations("RegisterPage");
  return (
    <div className="min-h-screen flex pt-8 justify-center bg-background py-8 px-4 sm:px-6 lg:px-8">
      {/* Background Image with Overlay */}
      <div className="absolute inset-0">
        <Image
          src="/login-background.jpg" 
          alt="Yezhome login background"
          fill={true}
          style={{ objectFit: 'cover' }}
          quality={80} // Adjust quality for better performance
          className="filter" // Slightly darken and blur the image
        />
        <div className="absolute inset-0 bg-black opacity-40"></div> {/* Dark overlay */}
      </div>
      <div className="max-w-2xl w-full space-y-4">
        <Card className="relative z-10 w-full max-w-2xl mx-auto p-6 bg-white shadow-2xl rounded-lg">
          <CardHeader className="text-center pb-6">
            <CardTitle className="text-3xl font-bold text-gray-800 mb-2">{t("title")}</CardTitle>
            <CardDescription className="text-gray-600">{t("description")}</CardDescription>
          </CardHeader>
          <CardContent>
            <RegisterForm />
          </CardContent>
          <CardFooter className="flex flex-col items-center pt-3 border-t border-gray-100 mt-3">
            <p className="text-sm text-gray-600">
              {t("loginPrompt")}{" "}
              <Link href="/dang-nhap" className="text-teal-600 hover:underline font-medium">
                {t("loginLink")}
              </Link>
            </p>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default RegisterPage;
