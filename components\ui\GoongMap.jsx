"use client";

import { useEffect, useRef } from "react";
import goongjs from "@goongmaps/goong-js";
import "@goongmaps/goong-js/dist/goong-js.css"; // Import GoongJS styles

const GoongMap = ({ selectedPlace, center, markerPosition, onMarkerDragEnd, isDisabled }) => {
  const mapContainerRef = useRef(null);
  const mapRef = useRef(null);
  const markerRef = useRef(null);

  useEffect(() => {
    goongjs.accessToken = `${process.env.NEXT_PUBLIC_GOONG_MAPTILES_KEY}`;

    // Initialize map with center position
    const initialCenter = center || { lng: 106.660172, lat: 10.762622 };
    
    const map = new goongjs.Map({
      container: mapContainerRef.current,
      style: "https://tiles.goong.io/assets/goong_map_highlight.json",
      center: [initialCenter.lng, initialCenter.lat],
      zoom: 15,
      crossSourceCollisions: false,
      interactive: !isDisabled, // Disable map interaction when form is disabled
    });

    mapRef.current = map;

    // Create marker with initial position
    const markerPos = markerPosition || center || (selectedPlace?.result?.geometry?.location && {
      lng: selectedPlace.result.geometry.location.lng,
      lat: selectedPlace.result.geometry.location.lat
    }) || initialCenter;

    const marker = new goongjs.Marker({
      draggable: !isDisabled, // Disable marker drag when form is disabled
      color: "#de5c5c",
    })
      .setLngLat([markerPos.lng, markerPos.lat])
      .addTo(map);

    markerRef.current = marker;

    // Add drag events only if not disabled
    if (!isDisabled) {      

      if (onMarkerDragEnd) {
        marker.on("dragend", (e) => {
          const { lng, lat } = marker.getLngLat();
          onMarkerDragEnd({ latLng: { lat: () => lat, lng: () => lng } });
        });
      }
    }

    return () => map.remove();
  }, [isDisabled]); // Add isDisabled to dependencies

  // Update marker position when it changes
  useEffect(() => {
    if (markerRef.current && markerPosition) {
      markerRef.current.setLngLat([markerPosition.lng, markerPosition.lat]);
    }
  }, [markerPosition]);

  // Update map center when it changes
  useEffect(() => {
    if (mapRef.current && center) {
      mapRef.current.setCenter([center.lng, center.lat]);
    }
  }, [center]);

  return (
    <div className={`w-full h-[500px] relative z-0 ${isDisabled ? 'opacity-75 cursor-not-allowed' : ''}`}>
      <div ref={mapContainerRef} className="w-full h-full" />
    </div>
  );
};

export default GoongMap;
