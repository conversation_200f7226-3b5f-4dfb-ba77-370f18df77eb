# Notification API Endpoints Documentation

Below is a comprehensive list of the API endpoints in the `NotificationController.cs` with their methods, request data, parameters, and response data formats.

## 1. Get All Notifications for Current User

**Method:** `GET`

**URL:** `/api/notifications`

**Query Parameters:**
- `fromDate` (optional): ISO 8601 date format (e.g., `2023-09-15T00:00:00Z`) - Filter notifications created after this date
- `toDate` (optional): ISO 8601 date format (e.g., `2023-09-30T23:59:59Z`) - Filter notifications created before this date
- `page` (optional): Integer, default is 1 - The page number for pagination
- `pageSize` (optional): Integer, default is 10 - Number of items per page

**Response Format:**
```json
{
  "items": [
    {
      "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "userId": "0194ca0d-9c32-724b-ab13-94684bb88af0",
      "type": "wallet_update",
      "title": "Wallet Top-up Successful",
      "message": "Your wallet has been topped up with 500,000 VND.",
      "isRead": false,
      "createdAt": "2023-09-15T10:30:45.123Z"
    },
    // More notifications...
  ],
  "totalCount": 25,
  "pageCount": 3,
  "currentPage": 1,
  "pageSize": 10
}
```

## 2. Get Notification By ID

**Method:** `GET`

**URL:** `/api/notifications/{id}`

**URL Parameters:**
- `id`: GUID - The ID of the notification to retrieve

**Response Format:**
```json
{
  "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "userId": "0194ca0d-9c32-724b-ab13-94684bb88af0",
  "type": "wallet_update",
  "title": "Wallet Top-up Successful",
  "message": "Your wallet has been topped up with 500,000 VND.",
  "isRead": false,
  "createdAt": "2023-09-15T10:30:45.123Z"
}
```

## 3. Get Notifications By Type

**Method:** `GET`

**URL:** `/api/notifications/by-type/{type}`

**URL Parameters:**
- `type`: String - The notification type (`news`, `wallet_update`, `promotion`, or `customer_message`)

**Query Parameters:**
- `fromDate` (optional): ISO 8601 date format - Filter by start date
- `toDate` (optional): ISO 8601 date format - Filter by end date
- `page` (optional): Integer, default is 1
- `pageSize` (optional): Integer, default is 10

**Response Format:** Same as "Get All Notifications" endpoint

## 4. Get Notifications By Type And User (Admin Only)

**Method:** `GET`

**URL:** `/api/notifications/by-type/{type}/user/{userId}`

**Authorization:** Requires admin privileges

**URL Parameters:**
- `type`: String - The notification type (`news`, `wallet_update`, `promotion`, or `customer_message`)
- `userId`: GUID - The ID of the user whose notifications to retrieve

**Query Parameters:**
- `fromDate` (optional): ISO 8601 date format - Filter by start date
- `toDate` (optional): ISO 8601 date format - Filter by end date
- `page` (optional): Integer, default is 1
- `pageSize` (optional): Integer, default is 10

**Response Format:** Same as "Get All Notifications" endpoint

## 6. Mark Notification As Read

**Method:** `PUT`

**URL:** `/api/notifications/{id}/mark-as-read`

**URL Parameters:**
- `id`: GUID - The ID of the notification to mark as read

**Response Format:** 
- Success: HTTP 204 No Content
- Not Found: HTTP 404 Not Found

## 7. Mark All Notifications As Read

**Method:** `PUT`

**URL:** `/api/notifications/mark-all-as-read`

**Response Format:**
- Success: HTTP 204 No Content

## 8. Delete Notification

**Method:** `DELETE`

**URL:** `/api/notifications/{id}`

**URL Parameters:**
- `id`: GUID - The ID of the notification to delete

**Response Format:**
- Success: HTTP 204 No Content
- Not Found: HTTP 404 Not Found

## 9. Get Unread Notification Count

**Method:** `GET`

**URL:** `/api/notifications/unread-count`

**Response Format:**
```json
{
  "count": 5
}
```

## Error Responses

All endpoints may return the following error responses:

### 400 Bad Request
```json
{
  "message": "User không hợp lệ"
}
```
or
```json
{
  "message": "Invalid notification type"
}
```

### 401 Unauthorized
Returned when the authorization token is missing or invalid.

### 403 Forbidden
Returned when trying to access admin-only endpoints without admin privileges.

### 404 Not Found
Returned when the requested resource is not found.

### 500 Internal Server Error
Returned for unexpected server errors.

## Note on Notification Types

Currently, the `IsValidNotificationType` method in the controller validates against `system`, `transaction`, `contact`, and `promotion`. This should be updated to validate against `news`, `wallet_update`, `customer_message`, and `promotion` to match the actual notification types used in your system.
