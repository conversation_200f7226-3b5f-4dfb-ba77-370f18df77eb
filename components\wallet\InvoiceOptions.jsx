  "use client"

  import { motion } from "framer-motion"
  import { FileText } from "lucide-react"
  import { Label } from "@/components/ui/label"
  import { Switch } from "@/components/ui/switch"
  import { Input } from "@/components/ui/input"
  import { formatCurrency } from "@/lib/utils"
  import { useTranslations } from 'next-intl';

  export function InvoiceOptions({
    needInvoice,
    setNeedInvoice,
    invoiceDetails,
    setInvoiceDetails,
    amount,
    selectedMethod,
    paymentMethods,
    t
  }) {

    const handleInvoiceDetailChange = (e) => {
      const { id, value } = e.target
      setInvoiceDetails({
        ...invoiceDetails,
        [id]: value,
      })
    }

    return (
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: -20 }}
        transition={{ duration: 0.3 }}
      >
        <div className="space-y-6">
          <div>
            <Label className="text-base">{t("invoiceInfoTitle")}</Label>
            <div className="flex items-center justify-between mt-4 p-4 border rounded-lg">
              <div className="flex items-center space-x-3">
                <FileText className="h-6 w-6 text-teal-600" />
                <div>
                  <div className="font-medium">{t("requestInvoice")}</div>
                  <div className="text-sm text-gray-500">{t("requestInvoiceDescription")}</div>
                </div>
              </div>
              <Switch 
                checked={needInvoice} 
                onCheckedChange={setNeedInvoice}
                className="data-[state=checked]:bg-teal-600" 
              />
            </div>
          </div>

          {needInvoice && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              className="space-y-4"
            >
              <div>
                <Label htmlFor="companyName">{t("invoiceCompanyNameLabel")}</Label>
                <Input
                  id="companyName"
                  className="mt-1"
                  value={invoiceDetails.companyName}
                  onChange={handleInvoiceDetailChange}
                />
              </div>
              <div>
                <Label htmlFor="taxId">{t("invoiceTaxCodeLabel")}</Label>
                <Input id="taxId" className="mt-1" value={invoiceDetails.taxId} onChange={handleInvoiceDetailChange} />
              </div>
              <div>
                <Label htmlFor="address">{t("invoiceAddressLabel")}</Label>
                <Input
                  id="address"
                  className="mt-1"
                  value={invoiceDetails.address}
                  onChange={handleInvoiceDetailChange}
                />
              </div>
            </motion.div>
          )}

          <div className="pt-4 space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-500">{t("depositAmount")}</span>
              <span>{formatCurrency(amount)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-500">{t("depositPaymentMethod")}</span>
              <span>{t(paymentMethods.find((m) => m.id === selectedMethod)?.name)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-500">{t("depositInvoice")}</span>
              <span>{needInvoice ? t("yes") : t("no")}</span>
            </div>
          </div>
        </div>
      </motion.div>
    )
  }
