'use client';

import { logout } from '@/app/actions/server/authenticate';
import { LogOutIcon } from 'lucide-react';
export default function ButtonLogOut() {
  return (
    <button
      className="flex items-center gap-3 rounded-lg py-2 text-sm font-medium text-red-600 transition-all hover:text-red-900 w-full"
      onClick={async () => {
        try {
          await logout();
        } catch (error) {
          console.error('Error logging out:', error);
        }
      }}
    >
      <LogOutIcon className="h-4 w-4" />
      Đ<PERSON>ng xuất
    </button>
  );
}