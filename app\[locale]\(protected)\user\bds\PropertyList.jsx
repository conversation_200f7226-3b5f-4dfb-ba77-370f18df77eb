"use client";
import { memo, useState, useCallback } from "react";
import { LoaderCircle, Trash2, Search, Zap, RefreshCcw } from "lucide-react";
import dynamic from "next/dynamic"; 
import { Button } from "@/components/ui/button"; 
import { useTranslations } from "next-intl";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { usePropertyList } from "@/hooks/usePropertyList";
import PropertyCard from "@/components/user-property/PropertyCard";

const NoData = dynamic(() => import("@/components/layout/NoData"));
const ContactRequestModal = dynamic(() => import("@/components/user-property/ContactRequestModal"), {
  ssr: false,
});
const HistoryModal = dynamic(() => import("@/components/user-property/HistoryModal"), {
  ssr: false,
});

function PropertyList({ initialData, initialFilterCounts }) {
  // Use the custom hook for state management and performance optimizations
  const {
    data,
    filteredData,
    loadingId,
    setLoadingId,
    searchTerm,
    setSearchTerm,
    activeFilter,
    setActiveFilter,
    selectedIds,
    setSelectedIds,
    currentPage,
    pageSize,
    filterCounts,
    isOverallLoading,
    isAllSelected,
    isIndeterminate,
    isEmptyDatabase,
    isEmptySearchResults,
    hasResults,
    fetchProperties,
    handleEdit,
    handleDelete,
    handleSendToReviewRequest,
    handleBulkHighlight,
    handleBulkRenew,
    handleBulkDelete,
    FilterKeys,
  } = usePropertyList(initialData, initialFilterCounts);

  const [selectedPropertyId, setSelectedPropertyId] = useState(null);
  const [isContactModalOpen, setIsContactModalOpen] = useState(false);
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);
  const tCommon = useTranslations("Common");
  const t = useTranslations("PropertyList");

  // Remaining handlers not in the hook
  const handleCheckboxChange = useCallback((propertyId, checked) => {
    setSelectedIds((prev) => {
      const newSet = new Set(prev);
      if (checked) {
        newSet.add(propertyId);
      } else {
        newSet.delete(propertyId);
      }
      return newSet;
    });
  }, [setSelectedIds]);

  const handleSelectAllChange = useCallback(
    (checked) => {
      if (checked) {
        const allIds = filteredData.map((p) => p.id);
        setSelectedIds(new Set(allIds));
      } else {
        setSelectedIds(new Set());
      }
    },
    [filteredData, setSelectedIds]
  );

  const handleShowContacts = useCallback((propertyId) => {
    setSelectedPropertyId(propertyId);
    setIsContactModalOpen(true);
  }, []); // State setters are stable, so empty deps are fine

  const handleShowHistory = useCallback((propertyId) => {
    setSelectedPropertyId(propertyId);
    setIsHistoryModalOpen(true);
  }, []); // State setters are stable, so empty deps are fine

  // --- Render ---
  return (
    <>
      {/* Search Input - Always visible, disabled during loading */}
      <div className="mb-4 flex flex-col sm:flex-row gap-4">
        <div className="relative grow">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            type="search"
            placeholder={t("searchPlaceholder")}
            className="pl-8 w-full"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            disabled={isOverallLoading}
          />
        </div>  
      </div>

      {/* Filter Buttons - Always visible, disabled during loading */}
      <div className="mb-4 flex flex-wrap gap-2 border-b relative">
        <Button
          variant={activeFilter === FilterKeys.ALL ? "solid" : "ghost"}
          size="sm"
          rounded="none"
          onClick={() => setActiveFilter(FilterKeys.ALL)}
          disabled={isOverallLoading}
        >
          {t("all")} ({filterCounts[FilterKeys.ALL]})
        </Button>
        <Button
          variant={activeFilter === FilterKeys.APPROVED ? "solid" : "ghost"}
          size="sm"
          rounded="none"
          onClick={() => setActiveFilter(FilterKeys.APPROVED)}
          disabled={isOverallLoading}
        >
          {t("approved")} ({filterCounts[FilterKeys.APPROVED]})
        </Button>
        <Button
          variant={activeFilter === FilterKeys.PENDING_APPROVAL ? "solid" : "ghost"}
          size="sm"
          rounded="none"
          onClick={() => setActiveFilter(FilterKeys.PENDING_APPROVAL)}
          disabled={isOverallLoading}
        >
          {t("pendingApproval")} ({filterCounts[FilterKeys.PENDING_APPROVAL]})
        </Button>
        <Button
          variant={activeFilter === FilterKeys.REJECTED_BY_ADMIN ? "solid" : "ghost"}
          size="sm"
          rounded="none"
          onClick={() => setActiveFilter(FilterKeys.REJECTED_BY_ADMIN)}
          disabled={isOverallLoading}
        >
          {t("rejectedByAdmin")} ({filterCounts[FilterKeys.REJECTED_BY_ADMIN]})
        </Button>
        <Button
          variant={activeFilter === FilterKeys.REJECTED_DUE_TO_UNPAID ? "solid" : "ghost"}
          size="sm"
          rounded="none"
          onClick={() => setActiveFilter(FilterKeys.REJECTED_DUE_TO_UNPAID)}
          disabled={isOverallLoading}
        >
          {t("rejectedDueToUnpaid")} ({filterCounts[FilterKeys.REJECTED_DUE_TO_UNPAID]})
        </Button>
        <Button
          variant={activeFilter === FilterKeys.WAITING_PAYMENT ? "solid" : "ghost"}
          size="sm"
          rounded="none"
          onClick={() => setActiveFilter(FilterKeys.WAITING_PAYMENT)}
          disabled={isOverallLoading}
        >
          {t("waitingPayment")} ({filterCounts[FilterKeys.WAITING_PAYMENT]})
        </Button>
        <Button
          variant={activeFilter === FilterKeys.EXPIRED ? "solid" : "ghost"}
          size="sm"
          rounded="none"
          onClick={() => setActiveFilter(FilterKeys.EXPIRED)}
          disabled={isOverallLoading}
        >
          {t("expired")} ({filterCounts[FilterKeys.EXPIRED]})
        </Button>
        <Button
          variant={activeFilter === FilterKeys.DRAFT ? "solid" : "ghost"}
          size="sm"
          rounded="none"
          onClick={() => setActiveFilter(FilterKeys.DRAFT)}
          disabled={isOverallLoading}
        >
          {t("draft")} ({filterCounts[FilterKeys.DRAFT]})
        </Button>
        <Button
          variant={activeFilter === FilterKeys.SOLD ? "solid" : "ghost"}
          size="sm"
          rounded="none"
          onClick={() => setActiveFilter(FilterKeys.SOLD)}
          disabled={isOverallLoading}
        >
          {t("sold")} ({filterCounts[FilterKeys.SOLD]})
        </Button>
      </div>

      {/* Bulk Actions - Always visible, disabled during loading */}
      <div className="mb-4 flex items-center gap-4 min-h-[40px]">
        <Checkbox
          id="select-all"
          checked={isAllSelected}
          onCheckedChange={handleSelectAllChange}
          aria-label="Select all properties on this page"
          data-state={isIndeterminate ? "indeterminate" : isAllSelected ? "checked" : "unchecked"}
          disabled={isOverallLoading}
        />
        <label htmlFor="select-all" className={`text-sm font-medium ${isOverallLoading ? 'text-gray-400' : ''}`}>
          {t("selectAll")}
        </label>
        {selectedIds.size > 0 && (
          <div className="flex gap-2 ml-auto">
            <Button
              size="sm"
              variant="outline"
              onClick={handleBulkHighlight}
              disabled={isOverallLoading || loadingId === "bulk-delete" || loadingId === "bulk-highlight"}
            >
              <Zap className="h-4 w-4 mr-1" /> {tCommon("highlight_status")} ({selectedIds.size})
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={handleBulkRenew}
              disabled={isOverallLoading || loadingId === "bulk-delete" || loadingId === "bulk-highlight"}
            >
              <RefreshCcw className="h-4 w-4 mr-1" /> {t("renew")} ({selectedIds.size})
            </Button>
            <Button
              size="sm"
              variant="destructive"
              onClick={handleBulkDelete}
              disabled={isOverallLoading || loadingId === "bulk-delete" || loadingId === "bulk-highlight"}
            >
              {loadingId === "bulk-delete" || loadingId === "bulk-highlight" ? (
                <LoaderCircle className="animate-spin h-4 w-4 mr-1" />
              ) : (
                <Trash2 className="h-4 w-4 mr-1" />
              )}
              {t("delete")} ({selectedIds.size})
            </Button>
          </div>
        )}
      </div>

      {/* Content Area - Conditional rendering based on loading and data states */}
      {isOverallLoading ? (
        <LoadingSpinner />
      ) : isEmptyDatabase ? (
        <NoData
          hasCreateButton={true}
          createMessage={t("noProperty")}
          createPageRoute="/user/bds/new"
          createButtonTitle={t("createProperty")}
        />
      ) : isEmptySearchResults ? (
        <NoData message={t("noResults")} />
      ) : hasResults ? (
        <>
          <div className="grid gap-4">
            {filteredData.map((property) => {
              const isSelected = selectedIds.has(property.id);
              return (
                <PropertyCard
                  key={property.id}
                  property={property}
                  onEdit={handleEdit}
                  onDelete={handleDelete}
                  onSendToReview={handleSendToReviewRequest}
                  onShowContacts={handleShowContacts}
                  onShowHistory={handleShowHistory}
                  loadingId={loadingId}
                  isSelected={isSelected}
                  onCheckboxChange={handleCheckboxChange}
                />
              );
            })}
          </div>

          {/* Pagination Controls */}
          {data.length > pageSize && (
            <div className="mt-6 flex justify-center items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => fetchProperties(activeFilter !== FilterKeys.ALL ? activeFilter : null, Math.max(1, currentPage - 1), pageSize)}
                disabled={currentPage <= 1 || isOverallLoading}
              >
                {t("previous")}
              </Button>
              <span className="text-sm">
                {t("page")} {currentPage}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => fetchProperties(activeFilter !== FilterKeys.ALL ? activeFilter : null, currentPage + 1, pageSize)}
                disabled={filteredData.length < pageSize || isOverallLoading}
              >
                {t("next")}
              </Button>
            </div>
          )}
        </>
      ) : null}

      {isContactModalOpen && (
        <ContactRequestModal propertyId={selectedPropertyId} open={isContactModalOpen} onClose={() => setIsContactModalOpen(false)} />
      )}

      {isHistoryModalOpen && <HistoryModal propertyId={selectedPropertyId} open={isHistoryModalOpen} onClose={() => setIsHistoryModalOpen(false)} />}
    </>
  );
}

export default memo(PropertyList);
