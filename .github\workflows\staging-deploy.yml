name: 🚀 Staging - Build & Deploy Frontend

on:
  push:
    branches:
      - main
    paths:
      - 'YEZHome_FE/**'
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: yezhome-fe-staging

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    environment: staging
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout source code
        uses: actions/checkout@v4

      - name: Set lowercase repo owner
        id: vars
        run: |
          echo "repo_owner_lower=${GITHUB_REPOSITORY_OWNER,,}" >> $GITHUB_ENV
          echo "timestamp=$(date +%s)" >> $GITHUB_ENV

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build Docker image with Nginx + SSL
        run: |
          docker build \
            -f Dockerfile.nginx \
            -t ${{ env.REGISTRY }}/${{ env.repo_owner_lower }}/${{ env.IMAGE_NAME }}:latest \
            -t ${{ env.REGISTRY }}/${{ env.repo_owner_lower }}/${{ env.IMAGE_NAME }}:${{ github.sha }} \
            -t ${{ env.REGISTRY }}/${{ env.repo_owner_lower }}/${{ env.IMAGE_NAME }}:${{ env.timestamp }} \
            .

      - name: Push Docker image
        run: |
          docker push ${{ env.REGISTRY }}/${{ env.repo_owner_lower }}/${{ env.IMAGE_NAME }}:latest
          docker push ${{ env.REGISTRY }}/${{ env.repo_owner_lower }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
          docker push ${{ env.REGISTRY }}/${{ env.repo_owner_lower }}/${{ env.IMAGE_NAME }}:${{ env.timestamp }}

      - name: Deploy to Staging Droplet with SSL
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.DROPLET_IP }}
          username: ${{ secrets.DROPLET_USERNAME }}
          key: ${{ secrets.DROPLET_SSH_KEY }}
          timeout: 600s
          command_timeout: 15m
          script: |
            echo "🚀 Deploying Frontend to Staging environment with SSL..."
            
            # Set environment variables
            export DEPLOY_PATH="/opt/yezhome/yezhome-frontend-staging"
            export IMAGE_TAG="${{ github.sha }}"
            
            # Display deployment context
            echo "📁 Deploy path: ${DEPLOY_PATH}"
            echo "🐳 Image tag: ${IMAGE_TAG}"
            echo "🌐 Domain: test.yezhome.vn"
            echo "🤖 Deployment type: Automatic (triggered by push to ${{ github.ref_name }})"
            echo "📝 Commit: ${{ github.sha }}"
            echo "🏗️  Build number: ${{ github.run_number }}"
            
            # Create deployment directory and copy files
            mkdir -p ${DEPLOY_PATH}
            
            # Create docker-compose override for this deployment
            cat > ${DEPLOY_PATH}/docker-compose.yml << 'EOF'
            services:
              yezhome-frontend-staging:
                image: ${{ env.REGISTRY }}/${{ env.repo_owner_lower }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
                container_name: yezhome-frontend-staging
                restart: always
                ports:
                  - "8080:80"
                  - "8443:443"
                environment:
                  # SSL Configuration
                  - DOMAIN=test.yezhome.vn
                  - CERTBOT_EMAIL=${{ secrets.CERTBOT_EMAIL }}
                  - CERTBOT_STAGING=true
                  
                  # Node.js Configuration
                  - NODE_ENV=staging
                  - NODE_TLS_REJECT_UNAUTHORIZED=${{ vars.NODE_TLS_REJECT_UNAUTHORIZED || '0' }}
                  
                  # API Configuration
                  - API_URL=${{ vars.API_URL || 'https://api-test.yezhome.vn' }}
                  - JWT_SECRET=${{ secrets.JWT_SECRET }}
                  
                  # UploadThing Configuration
                  - UPLOADTHING_TOKEN=${{ secrets.UPLOADTHING_TOKEN }}
                  
                  # Goong Maps Configuration (Server-side)
                  - GOONG_MAPTILES_KEY=${{ secrets.GOONG_MAPTILES_KEY }}
                  - GOONG_GEO_API_KEY=${{ secrets.GOONG_GEO_API_KEY }}
                  
                  # Next.js Public Variables (exposed to browser)
                  - NEXT_PUBLIC_API_URL=${{ vars.NEXT_PUBLIC_API_URL || 'https://api-test.yezhome.vn' }}
                  - NEXT_PUBLIC_INTERNAL_API_URL=${{ vars.NEXT_PUBLIC_INTERNAL_API_URL || 'https://adapi-test.yezhome.vn' }}
                  - NEXT_PUBLIC_FRONTEND_URL=${{ vars.NEXT_PUBLIC_FRONTEND_URL || 'https://test.yezhome.vn' }}
                  - NEXT_PUBLIC_GOONG_MAPTILES_KEY=${{ secrets.NEXT_PUBLIC_GOONG_MAPTILES_KEY }}
                  - NEXT_PUBLIC_GOONG_GEO_API_KEY=${{ secrets.NEXT_PUBLIC_GOONG_GEO_API_KEY }}
                  - NEXT_PUBLIC_ANALYTICS_ID=${{ vars.NEXT_PUBLIC_ANALYTICS_ID }}
                  
                  # Firebase Configuration (Public)
                  - NEXT_PUBLIC_FIREBASE_API_KEY=${{ secrets.NEXT_PUBLIC_FIREBASE_API_KEY }}
                  - NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=${{ vars.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN }}
                  - NEXT_PUBLIC_FIREBASE_PROJECT_ID=${{ vars.NEXT_PUBLIC_FIREBASE_PROJECT_ID }}
                  - NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=${{ vars.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET }}
                  - NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=${{ vars.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID }}
                  - NEXT_PUBLIC_FIREBASE_APP_ID=${{ vars.NEXT_PUBLIC_FIREBASE_APP_ID }}
                  - NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=${{ vars.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID }}
                  
                  # Deployment Metadata
                  - IMAGE_TAG=${{ github.sha }}
                  - BUILD_NUMBER=${{ github.run_number }}
                  - COMMIT_SHA=${{ github.sha }}
                  - LAST_DEPLOYED=$(date -u '+%Y-%m-%d %H:%M:%S UTC')
                  - DEPLOYED_BY=${{ github.actor }}
                volumes:
                  - frontend_ssl_certs_staging:/etc/letsencrypt
                  - frontend_ssl_challenges_staging:/var/www/certbot
                networks:
                  - yezhome-frontend-staging-network
                labels:
                  - "environment=staging"
                  - "domain=test.yezhome.vn"
                healthcheck:
                  test: ["CMD", "curl", "-f", "http://localhost/health"]
                  interval: 30s
                  timeout: 10s
                  retries: 3
                  start_period: 40s
            
            volumes:
              frontend_ssl_certs_staging:
                name: yezhome_staging_frontend_ssl_certs
              frontend_ssl_challenges_staging:
                name: yezhome_staging_frontend_ssl_challenges
            
            networks:
              yezhome-frontend-staging-network:
                name: yezhome-frontend-staging-network
                driver: bridge
            EOF
            
            # Login to GitHub Container Registry
            echo "🔐 Logging into GitHub Container Registry..."
            echo "${{ secrets.PULL_PAT }}" | docker login ${{ env.REGISTRY }} -u ${{ github.actor }} --password-stdin
            
            # Navigate to deployment directory
            cd ${DEPLOY_PATH}
            
            # Pull the latest image
            echo "📥 Pulling latest image..."
            docker pull ${{ env.REGISTRY }}/${{ env.repo_owner_lower }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
            
            # Stop existing services
            echo "🛑 Stopping existing services..."
            docker compose down || true
            
            # Start services with SSL
            echo "🚀 Starting services with SSL..."
            docker compose up -d
            
            # Wait for services to be ready
            echo "⏳ Waiting for services to be ready (SSL generation may take time)..."
            sleep 60
            
            # Check SSL certificate generation
            echo "🔍 Checking SSL certificates for staging..."
            timeout 300 bash -c "until curl -sSf https://test.yezhome.vn/health >/dev/null 2>&1; do sleep 10; done" && echo "✅ test.yezhome.vn SSL working" || echo "⚠️ SSL may still be setting up"
            
            # Verify deployment
            if ! docker ps | grep -q yezhome-frontend-staging; then
              echo "❌ Deployment failed - container not running"
              docker logs yezhome-frontend-staging || true
              exit 1
            fi
            
            # Clean up old images (keep last 3)
            echo "🧹 Cleaning up old images..."
            docker images ${{ env.REGISTRY }}/${{ env.repo_owner_lower }}/${{ env.IMAGE_NAME }} --format "table {{.Tag}}\t{{.CreatedAt}}" | tail -n +4 | awk '{print $1}' | xargs -r -I {} docker rmi ${{ env.REGISTRY }}/${{ env.repo_owner_lower }}/${{ env.IMAGE_NAME }}:{} || true
            docker image prune -f || true
            
            # Logout from registry
            echo "🔐 Logging out of GitHub Container Registry..."
            docker logout ${{ env.REGISTRY }}
            
            echo "✅ Staging deployment with SSL completed successfully!"
            echo ""
            echo "📊 Deployment Summary:"
            echo "  Environment: Staging"
            echo "  Image Tag: ${IMAGE_TAG}"
            echo "  Build: #${{ github.run_number }}"
            echo "  Commit: ${{ github.sha }}"
            echo "  Frontend URL: https://test.yezhome.vn"
            echo "  Completed: $(date -u '+%Y-%m-%d %H:%M:%S UTC')"

      - name: Notify Deployment Status
        if: always()
        run: |
          if [ "${{ job.status }}" == "success" ]; then
            echo "✅ Staging deployment with SSL successful!"
            echo "🚀 Frontend URL: https://test.yezhome.vn"
            echo "📦 Image: ${{ env.REGISTRY }}/${{ env.repo_owner_lower }}/${{ env.IMAGE_NAME }}:${{ github.sha }}"
          else
            echo "❌ Staging deployment failed!"
          fi
