# Homepage Feature - Development Tasks

## App Router Structure
- [x] Verify main page structure at `app/[locale]/page.jsx` integrates necessary components.
- [x] Confirm dynamic route parameters (`[locale]`) are handled correctly for internationalization.

## API Routes / Server Actions
*(Note: These are Server Actions, interacting with backend logic)*
- **Dependency:** Backend services/database for properties and user favorites must be available.
- [x] Ensure `searchProperties` server action (`@/app/actions/server/property.jsx`) correctly implements all filter parameters specified in `homepage.md` (FR1).
  - [ ] Add server-side validation and sanitization for all filter inputs in `searchProperties`.
- [x] Ensure `searchProperties` action returns the correct data structure including pagination info (`items`, `totalCount`, etc.).
- [x] Verify `checkFavoriteStatus` server action (`@/app/actions/server/userFavorite.jsx`) correctly identifies favorited properties for a logged-in user.
  - [x] Add server-side authentication check to `checkFavoriteStatus`.
- [x] Verify `addToFavorites` server action (`@/app/actions/server/userFavorite.jsx`) correctly adds a property to user favorites.
  - [x] Add server-side authentication check to `addToFavorites`.
- [x] Verify `removeFromFavorites` server action (`@/app/actions/server/userFavorite.jsx`) correctly removes a property from user favorites.
  - [x] Add server-side authentication check to `removeFromFavorites`.
- [x] Implement initial favorite status fetching using `checkFavoriteStatus` server action (consider adding loading state). (Completed)
- [x] Implement main data fetching logic using `searchProperties` server action (`useEffect` based on `filterCriteria`, `userLocation`). (Completed)
  - [x] Handle loading state (`setLoading`). (Completed)
  - [x] Handle success: update `filteredProperties` and `paginationInfo` state. (Completed)
  - [x] Handle errors: display toast notifications. (Completed)

## UI Components
- **`SearchFilterComponent` (`@/components/property/SearchFilter.jsx`)**
  - [x] Verify all filter inputs specified in FR1 exist and are functional.
  - [x] Ensure `onFilterChange` callback correctly passes the filter criteria object.
  - [x] Implement/verify initial filter population from `initialFilters` prop.
- **`MapSectionComponent` (`@/components/property/MapSection.jsx`)**
  - [x] Implement map initialization and centering logic (user geolocation fallback to default). (Completed)
  - [x] Implement rendering of property markers based on the `properties` prop. (Completed)
  - [x] Implement visual highlighting for the selected property marker based on the `selectedProperty` prop (FR10 - e.g., change color, size). (Completed)
- [x] Implement displaying loading state (`loading` prop). (Completed)
- [x] Implement displaying "No results" message when `properties` array is empty. (Completed)
- [x] Implement display of result count and current page info from `pagination` prop. (Completed)
- [x] Implement responsive grid layout for `PropertyCard`s. (Completed)
- [x] Pass necessary props (`isLoggedIn`, `onToggleFavorite`) down to `PropertyCard`. (Completed)
- [x] Implement `handleToggleFavorite` logic to update local state and dispatch `favorites-changed` event. (Completed)
- [x] Verify correct display of all property details (image, name, price, stats, etc.). (Completed)
- [x] Implement favorite button logic (`handleFavoriteClick`):
  - [x] Check `isLoggedIn` prop before calling server actions.
  - [x] Call `addToFavorites`/`removeFromFavorites` based on current state.
  - [x] Handle API call loading state (`isLoading`).
  - [x] Display toast notifications on success/error/login required.
  - [x] Update local `favorite` state and call `onToggleFavorite`.
- [x] Ensure `onClick` prop is correctly called with the property data when the card (or details button) is clicked. (Completed)
- [x] Verify correct rendering of page numbers (`getPageNumbers`). (Completed)
- [x] Verify correct enabling/disabling of prev/next buttons based on `pagination` prop. (Completed)
- [x] Ensure `onPageChange` callback is correctly called with the target page number. (Completed)
- [x] Ensure modal displays all relevant property details passed via the `property` prop. (Completed)
- [x] Ensure `onClose` callback is triggered when the modal is closed by the user. (Completed)
- [x] Implement logic to pass the `isLoggedIn` status down to `PropertyListComponent`. (Requires integrating auth state). (Completed)
- [x] Implement geolocation fetching logic (`useEffect`). (Completed)
- [x] Implement initial URL parameter parsing (`useEffect`, `useSearchParams`, `parseUrlToFilterCriteria`). (Completed)
- [x] Implement `handleFilterChange` callback: update `filterCriteria`, reset page to 1. (Completed)
- [x] Implement `handlePageChange` callback: update `filterCriteria.page`. (Completed)
- [x] Implement `handlePropertySelect` callback:
  - [x] Update `selectedProperty`, `selectedPropertyForModal`, `mapCenter` states. (Completed)
  - [x] Implement URL update logic to include `propertyId` alongside filters (`filterCriteriaToUrlParams`, `router.push`). (Completed)
- [x] Implement modal `onClose` handler:
  - [x] Update `selectedPropertyForModal`, `selectedProperty` states. (Completed)
  - [x] Implement URL update logic to remove `propertyId` (`filterCriteriaToUrlParams`, `router.push`). (Completed)
- [x] Implement `useEffect` to sync `filterCriteria` changes (excluding initial load/modal changes) to the URL. (Completed)
- [x] Verify dynamic imports (`dynamic`) are used for `SearchFilter`, `MapSection`, `PropertyList`. (Completed)
- [x] Verify `React.memo` is used effectively on `PropertyCard` and `PropertyList`. (Completed)
- [x] Verify `useCallback` is used for handlers passed to memoized components (`handleFilterChange`, `handlePageChange`, `handlePropertySelect`). (Completed)
- [x] Review `IntersectionObserver` usage in `PropertyList` for necessity alongside `next/image`. (Completed - Found redundant)
- [x] Remove redundant IntersectionObserver code from `components/property/PropertyList.jsx`. (Completed)
- N/A **(Enhancement):** Implement debouncing for text-based filter inputs (Search is button-triggered).
