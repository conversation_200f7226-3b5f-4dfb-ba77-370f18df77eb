# Default server configuration for YEZHome Frontend
# This configuration will be updated by the startup script based on environment

# HTTP server - handles Let's Encrypt challenges and redirects to HTTPS
server {
    listen 80;
    server_name _;
    
    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # Let's Encrypt challenge
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
        try_files $uri $uri/ =404;
    }
    
    # Redirect all other HTTP traffic to HTTPS (will be enabled after SSL setup)
    location / {
        # Initially serve content over HTTP until SSL is configured
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
        
        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Referrer-Policy "strict-origin-when-cross-origin";
        
        # Cache control for static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Vary Accept-Encoding;
        }
        
        # Cache control for HTML files
        location ~* \.html$ {
            expires 1h;
            add_header Cache-Control "public, must-revalidate";
        }
    }
}

# HTTPS server template (will be activated after SSL certificate generation)
# server {
#     listen 443 ssl http2;
#     server_name DOMAIN_PLACEHOLDER;
#     
#     # SSL Configuration
#     ssl_certificate /etc/letsencrypt/live/DOMAIN_PLACEHOLDER/fullchain.pem;
#     ssl_certificate_key /etc/letsencrypt/live/DOMAIN_PLACEHOLDER/privkey.pem;
#     
#     # SSL Security
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
#     ssl_prefer_server_ciphers off;
#     ssl_session_cache shared:SSL:10m;
#     ssl_session_timeout 10m;
#     
#     # Security headers
#     add_header X-Frame-Options DENY;
#     add_header X-Content-Type-Options nosniff;
#     add_header X-XSS-Protection "1; mode=block";
#     add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
#     add_header Referrer-Policy "strict-origin-when-cross-origin";
#     
#     # Rate limiting
#     limit_req zone=general burst=50 nodelay;
#     
#     # Root directory
#     root /usr/share/nginx/html;
#     index index.html index.htm;
#     
#     # Main location block
#     location / {
#         try_files $uri $uri/ /index.html;
#         
#         # Cache control for static assets
#         location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
#             expires 1y;
#             add_header Cache-Control "public, immutable";
#             add_header Vary Accept-Encoding;
#         }
#         
#         # Cache control for HTML files
#         location ~* \.html$ {
#             expires 1h;
#             add_header Cache-Control "public, must-revalidate";
#         }
#     }
#     
#     # Special handling for API routes (if needed)
#     location /api/ {
#         # Rate limiting for API calls
#         limit_req zone=login burst=10 nodelay;
#         
#         # Proxy to API server (adjust as needed)
#         proxy_pass http://api.yezhome.vn/;
#         proxy_http_version 1.1;
#         proxy_set_header Upgrade $http_upgrade;
#         proxy_set_header Connection 'upgrade';
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto $scheme;
#         proxy_cache_bypass $http_upgrade;
#     }
# }
