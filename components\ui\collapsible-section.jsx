"use client";

import * as React from "react";
import { ChevronDown } from "lucide-react";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

export function CollapsibleSection({ title, subTitle, children, defaultOpen = true, className = "" }) {
  const [isOpen, setIsOpen] = React.useState(defaultOpen);

  return (
    <Collapsible open={isOpen} onOpenChange={setIsOpen} className={cn("group", className)}>
      <CollapsibleTrigger asChild>
        <Button
          variant="ghost"
          className="flex w-full justify-between p-4 text-left hover:bg-muted/50 transition-all duration-200 ease-in-out group-hover:shadow-sm"
        >
          <h4 className="font-semibold text-gray-800 mb-0">
            {title}
            {subTitle && <span className="text-xs ml-2 text-gray-400">({subTitle})</span>}
          </h4>
          <ChevronDown className={cn("h-4 w-4 transition-all duration-300 ease-in-out", isOpen && "rotate-180")} />
        </Button>
      </CollapsibleTrigger>
      <CollapsibleContent className="overflow-hidden transition-all duration-300 ease-in-out data-[state=closed]:animate-collapsible-up data-[state=open]:animate-collapsible-down">
        <div className="px-4 pb-4 animate-fade-in">{children}</div>
      </CollapsibleContent>
    </Collapsible>
  );
}
