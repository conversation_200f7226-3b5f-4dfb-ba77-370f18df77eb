import { getPropertyById } from "@/app/actions/server/property";
import SuccessPageClient from "./SuccessPageClient";
import NotFoundPage from "@/app/[locale]/not-found";

const SuccessPage = async ({ params }) => {
  const { propertyId } = await params;
  const response = await getPropertyById(propertyId);

  if (!response.data) {
    return <NotFoundPage />;
  }

  return <SuccessPageClient property={response.data} />;
};

export default SuccessPage;
