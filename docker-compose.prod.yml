version: '3.8'

services:
  yezhome-frontend-prod:
    build:
      context: .
      dockerfile: Dockerfile.nginx
    container_name: yezhome-frontend-prod
    restart: always
    ports:
      - "80:80"
      - "443:443"
    environment:
      - DOMAIN=yezhome.vn
      - CERTBOT_EMAIL=${CERTBOT_EMAIL}
      - CERTBOT_STAGING=false
    volumes:
      # Persistent storage for SSL certificates
      - frontend_ssl_certs:/etc/letsencrypt
      - frontend_ssl_challenges:/var/www/certbot
      # Optional: Custom nginx configurations
      - ./nginx/custom:/etc/nginx/custom:ro
    networks:
      - yezhome-frontend-network
    labels:
      - "environment=production"
      - "domain=yezhome.vn"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  frontend_ssl_certs:
    name: yezhome_prod_frontend_ssl_certs
  frontend_ssl_challenges:
    name: yezhome_prod_frontend_ssl_challenges

networks:
  yezhome-frontend-network:
    name: yezhome-frontend-prod-network
    driver: bridge
