# YEZHome Frontend Deployment Workflows

This directory contains GitHub Actions workflows for deploying the YEZHome frontend application to different environments.

## Workflows

### 1. Staging Deployment (`staging-deploy.yml`)
- **Trigger**: Automatic on push to `main` or `develop` branches
- **Environment**: Staging
- **Purpose**: Continuous deployment for testing and validation

### 2. Production Deployment (`production-deploy.yml`)
- **Trigger**: Manual only (workflow_dispatch)
- **Environment**: Production  
- **Purpose**: Controlled production releases with confirmation

## Required Secrets and Variables

Configure these in your GitHub repository settings:

### Secrets (Repository Settings → Secrets and variables → Actions → Secrets)
- `DROPLET_IP`: Your droplet/server IP address
- `DROPLET_USERNAME`: SSH username for server access (e.g., "root" or "deploy")
- `DROPLET_SSH_KEY`: Private SSH key for server access
- `PULL_PAT`: Personal Access Token for pulling Docker images (use GITHUB_TOKEN or create PAT)

### Variables (Repository Settings → Secrets and variables → Actions → Variables)

#### Staging Environment Variables
- `STAGING_FRONTEND_PORT`: Port for staging frontend container (optional, defaults to 3001)
- `STAGING_API_URL`: Backend API URL for staging (e.g., "https://api.staging.yezhome.com")
- `STAGING_INTERNAL_API_URL`: Internal API URL for staging (e.g., "https://internal-api.staging.yezhome.com")
- `STAGING_FRONTEND_URL`: Frontend URL for staging (e.g., "https://staging.yezhome.com")

#### Production Environment Variables
- `PRODUCTION_FRONTEND_PORT`: Port for production frontend container (optional, defaults to 3000)
- `PRODUCTION_API_URL`: Backend API URL for production (e.g., "https://api.yezhome.com")
- `PRODUCTION_INTERNAL_API_URL`: Internal API URL for production (e.g., "https://internal-api.yezhome.com")
- `PRODUCTION_FRONTEND_URL`: Frontend URL for production (e.g., "https://yezhome.com")
- `PRODUCTION_ANALYTICS_ID`: Analytics tracking ID (optional)

### Automatically Provided
- `GITHUB_TOKEN`: Automatically provided by GitHub Actions for container registry access

## Environment Setup

### GitHub Environments
1. Go to Repository Settings → Environments
2. Create `staging` and `production` environments
3. Configure protection rules for production (require reviews, restrict branches)

### Server Prerequisites
Ensure your droplets have:
- Docker installed and running
- SSH access configured
- Firewall rules allowing the specified ports
- Sufficient resources (recommended: 2GB RAM, 1 CPU minimum)

## Usage

### Staging Deployment
Staging deployments happen automatically when you:
- Push to `main` branch
- Push to `develop` branch
- Manually trigger via GitHub Actions UI

### Production Deployment
Production deployments require manual approval:
1. Go to GitHub Actions → Production - Build & Deploy Frontend
2. Click "Run workflow"
3. Type "DEPLOY" in the confirmation field
4. Optionally specify an image tag (defaults to latest commit)
5. Click "Run workflow"

## Features

### Staging Workflow Features
- Automatic deployment on code changes
- Health checks after deployment
- Image cleanup (keeps last 3 images)
- Container restart policies
- Environment-specific configuration

### Production Workflow Features
- Manual confirmation required ("DEPLOY" must be typed)
- Automatic backup before deployment
- Rollback capability on failure
- Enhanced health checks with retries
- Resource limits for containers
- Comprehensive logging and status reporting

## Monitoring

Both workflows include:
- Container health verification
- Application health checks via HTTP
- Deployment status notifications
- Error logging and troubleshooting information

## Troubleshooting

### Common Issues
1. **SSH Connection Failed**: Check SSH key format and server access
2. **Docker Pull Failed**: Verify GitHub token permissions
3. **Health Check Failed**: Check application startup time and endpoint availability
4. **Port Conflicts**: Ensure specified ports are available on the server

### Logs
- Check GitHub Actions logs for detailed error information
- SSH into your droplet and run `docker logs <container_name>` for application logs
- Use `docker ps` to verify container status

## Security Notes

- SSH keys should be generated specifically for deployment
- Use environment protection rules for production
- Regularly rotate SSH keys and access tokens
- Monitor deployment logs for suspicious activity
