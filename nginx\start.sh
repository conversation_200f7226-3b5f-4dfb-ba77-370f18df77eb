#!/bin/sh

# Startup script for YEZHome Frontend with <PERSON>in<PERSON> and SSL

set -e

echo "Starting YEZHome Frontend with Nginx and SSL support"

# Environment variables
DOMAIN=${DOMAIN:-"localhost"}
echo "Domain: $DOMAIN"

# Start crond for certificate renewal
crond

# Start Nginx in background
echo "Starting Nginx..."
nginx

# Wait a moment for <PERSON><PERSON><PERSON> to fully start
sleep 2

# Setup SSL in background (non-blocking)
echo "Setting up SSL..."
/usr/local/bin/ssl-setup.sh &

# Keep the container running and monitor Nginx
echo "Frontend server is ready!"
echo "HTTP: http://$DOMAIN"
if [ "$DOMAIN" != "localhost" ] && [ "$DOMAIN" != "" ]; then
    echo "HTTPS: https://$DOMAIN (will be available after SSL setup completes)"
fi

# Monitor Nginx process
while true; do
    if ! pgrep nginx > /dev/null; then
        echo "Nginx stopped unexpectedly, restarting..."
        nginx
    fi
    sleep 30
done
