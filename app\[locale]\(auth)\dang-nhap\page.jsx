import { Link } from "@/i18n/navigation";
import { getTranslations } from "next-intl/server";
import LoginForm from "@/components/auth/LoginForm";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import Image from "next/image";

const LoginPage = async () => {
  const t = await getTranslations("LoginPage");
  return (
    <div className="min-h-screen flex pt-8 justify-center bg-background px-4 sm:px-6 lg:px-8">
      {/* Background Image with Overlay */}
      <div className="absolute inset-0">
        <Image
          src="/login-background.jpg" 
          alt="Yezhome login background"
          fill={true}
          style={{ objectFit: 'cover' }}
          quality={80} // Adjust quality for better performance
          className="filter" // Slightly darken and blur the image
        />
        <div className="absolute inset-0 bg-black opacity-40"></div> {/* Dark overlay */}
      </div>
      <div className="max-w-md w-full space-y-8">
        <Card className="relative z-10 w-full max-w-sm mx-auto p-6 bg-white shadow-2xl rounded-lg">
          <CardHeader className="text-center pb-6">
            <CardTitle className="text-3xl font-bold text-gray-800 mb-2">{t("title")}</CardTitle>
            <CardDescription className="text-gray-600">{t("description")}</CardDescription>
          </CardHeader>
          <CardContent>
            <LoginForm />
          </CardContent>
          <CardFooter className="flex flex-col items-center pt-6 border-t border-gray-100 mt-6">
            <p className="text-sm text-gray-600">
              {t("signUpPrompt")}{" "}
              <Link href="/dang-ki" className="text-teal-600 hover:underline font-medium">
                {t("signUpLink")}
              </Link>
            </p>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default LoginPage;
